/**
 * JavaScript for Category Meta Fields
 */
(function($) {
    'use strict';
    
    // Document ready
    $(document).ready(function() {
        // Image upload
        $('.category-image-upload').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var container = button.closest('.category-image-container');
            var imagePreview = container.find('.category-image-preview');
            var imageInput = container.find('#category-image');
            var removeButton = container.find('.category-image-remove');
            
            // Create a media frame
            var frame = wp.media({
                title: 'Select or Upload Category Image',
                button: {
                    text: 'Use this image'
                },
                multiple: false
            });
            
            // When an image is selected in the media frame...
            frame.on('select', function() {
                // Get media attachment details from the frame state
                var attachment = frame.state().get('selection').first().toJSON();
                
                // Set the image URL
                imagePreview.attr('src', attachment.url);
                
                // Set the image ID in the hidden input
                imageInput.val(attachment.id);
                
                // Show the remove button
                removeButton.show();
            });
            
            // Finally, open the modal
            frame.open();
        });
        
        // Image remove
        $('.category-image-remove').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var container = button.closest('.category-image-container');
            var imagePreview = container.find('.category-image-preview');
            var imageInput = container.find('#category-image');
            
            // Set the image URL to placeholder
            imagePreview.attr('src', advanceCouponCategoryMeta.placeholderUrl);
            
            // Clear the image ID
            imageInput.val('');
            
            // Hide the remove button
            button.hide();
        });
        
        // Icon select
        $('.category-icon-select').on('click', function(e) {
            e.preventDefault();
            
            var button = $(this);
            var container = button.closest('.category-icon-container');
            var iconPicker = container.siblings('.category-icon-picker');
            
            // Toggle the icon picker
            iconPicker.toggle();
        });
        
        // Icon item click
        $('.icon-item').on('click', function() {
            var icon = $(this).data('icon');
            var container = $(this).closest('.term-icon-wrap');
            var iconInput = container.find('.category-icon-input');
            var iconPreview = container.find('.category-icon-preview i');
            
            // Set the icon class
            iconInput.val(icon);
            iconPreview.attr('class', icon);
            
            // Hide the icon picker
            container.find('.category-icon-picker').hide();
        });
        
        // Icon search
        $('.icon-search-input').on('keyup', function() {
            var search = $(this).val().toLowerCase();
            var container = $(this).closest('.category-icon-picker');
            var icons = container.find('.icon-item');
            
            icons.each(function() {
                var icon = $(this).data('icon').toLowerCase();
                if (icon.indexOf(search) > -1) {
                    $(this).show();
                } else {
                    $(this).hide();
                }
            });
        });
        
        // Update icon preview when input changes
        $('.category-icon-input').on('change keyup', function() {
            var input = $(this);
            var container = input.closest('.category-icon-container');
            var iconPreview = container.find('.category-icon-preview i');
            
            // Set the icon class
            iconPreview.attr('class', input.val());
        });
    });
    
})(jQuery);
