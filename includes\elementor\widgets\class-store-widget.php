<?php
namespace AdvanceCoupon\Elementor\Widgets;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Store Widget for Elementor
 */
class Store_Widget extends \Elementor\Widget_Base {
    /**
     * Get widget name
     */
    public function get_name() {
        return 'store_widget';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return __('Stores', 'advance-coupon');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-cart-medium'; // Updated to a better shop icon
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['general'];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['store', 'shop', 'merchant'];
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => __('Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Stores', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'show_all_stores',
            [
                'label' => __('Show All Stores (Archive Mode)', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
                'description' => __('Enable this for store archive pages. This will remove the total store limit while keeping pagination.', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'limit',
            [
                'label' => __('Number of Stores', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 100,
                'step' => 1,
                'default' => 10,
                // No condition - always show this control
                'description' => __('Limit the number of stores to display. This setting is used when pagination is disabled. If pagination is enabled, use "Stores Per Page" instead.', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'columns',
            [
                'label' => __('Columns', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => '4',
                'options' => [
                    '1' => '1',
                    '2' => '2',
                    '3' => '3',
                    '4' => '4',
                    '5' => '5',
                    '6' => '6',
                ],
            ]
        );

        $this->add_control(
            'image_height',
            [
                'label' => __('Image Height', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 50,
                        'max' => 300,
                        'step' => 10,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 120,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-thumbnail' => 'height: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'show_coupon_count',
            [
                'label' => __('Show Coupon Count', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'badge_text',
            [
                'label' => __('Badge Text Format', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('%d Coupons', 'advance-coupon'),
                'description' => __('Use %d as a placeholder for the count number', 'advance-coupon'),
                'condition' => [
                    'show_coupon_count' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'badge_alignment',
            [
                'label' => __('Badge Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'inline',
                'options' => [
                    'inline' => __('Inline with Title', 'advance-coupon'),
                    'right' => __('Right Aligned', 'advance-coupon'),
                ],
                'condition' => [
                    'show_coupon_count' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'image_title_spacing',
            [
                'label' => __('Image to Title Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 15,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-title-wrapper' => 'margin-top: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'title_button_spacing',
            [
                'label' => __('Title to Button Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 10,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-actions' => 'margin-top: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'button_text',
            [
                'label' => __('View Coupons Button Text', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('View Coupons', 'advance-coupon'),
                'placeholder' => __('Enter button text', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination',
            [
                'label' => __('Show Pagination', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
            ]
        );

        $this->add_control(
            'stores_per_page',
            [
                'label' => __('Stores Per Page', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 50,
                'step' => 1,
                'default' => 8,
                'condition' => [
                    'pagination' => 'yes',
                ],
                'description' => __('Number of stores to display per page when pagination is enabled. If "Show All Stores" is enabled, this controls how many stores appear on each page.', 'advance-coupon'),
            ]
        );

        $this->end_controls_section();

        $this->start_controls_section(
            'store_title_style_section',
            [
                'label' => __('Store Title Style', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .store-title a' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'title_hover_color',
            [
                'label' => __('Text Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .store-title a:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-title',
            ]
        );

        $this->add_responsive_control(
            'title_text_align',
            [
                'label' => __('Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'center',
                'selectors' => [
                    '{{WRAPPER}} .store-title' => 'text-align: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'title_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '0',
                    'right' => '0',
                    'bottom' => '10',
                    'left' => '0',
                    'unit' => 'px',
                ],
            ]
        );

        $this->add_control(
            'title_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-title' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // General Style Section
        $this->start_controls_section(
            'general_style_section',
            [
                'label' => __('General Style', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'widget_title_color',
            [
                'label' => __('Widget Title Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .store-widget-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'widget_title_typography',
                'label' => __('Widget Title Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-widget-title',
            ]
        );

        $this->add_responsive_control(
            'widget_title_align',
            [
                'label' => __('Widget Title Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'left',
                'selectors' => [
                    '{{WRAPPER}} .store-widget-title' => 'text-align: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'widget_title_margin',
            [
                'label' => __('Widget Title Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-widget-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '0',
                    'right' => '0',
                    'bottom' => '20',
                    'left' => '0',
                    'unit' => 'px',
                ],
            ]
        );

        $this->add_control(
            'store_bg_color',
            [
                'label' => __('Store Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'background-color: {{VALUE}}',
                ],
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'store_text_color',
            [
                'label' => __('Store Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'store_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-item',
            ]
        );

        $this->add_control(
            'store_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'store_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-item',
            ]
        );

        $this->end_controls_section();

        // View Coupons Button Style Section
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => __('Button Style', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        // Start tabs for Normal and Hover states
        $this->start_controls_tabs('button_style_tabs');

        // Normal state tab
        $this->start_controls_tab(
            'button_style_normal',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '4',
                    'right' => '4',
                    'bottom' => '4',
                    'left' => '4',
                    'unit' => 'px',
                ],
            ]
        );

        $this->add_control(
            'button_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '10',
                    'right' => '20',
                    'bottom' => '10',
                    'left' => '20',
                    'unit' => 'px',
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state tab
        $this->start_controls_tab(
            'button_style_hover',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'button_hover_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .view-store:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#339af0',
                'selectors' => [
                    '{{WRAPPER}} .view-store:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store:hover' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store:hover' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_transition',
            [
                'label' => __('Transition Duration', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'range' => [
                    'px' => [
                        'min' => 0.1,
                        'max' => 2,
                        'step' => 0.1,
                    ],
                ],
                'default' => [
                    'size' => 0.3,
                ],
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'transition: all {{SIZE}}s ease;',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .view-store',
                'separator' => 'before',
            ]
        );

        $this->end_controls_section();

        // Coupon Count Badge Style Section
        $this->start_controls_section(
            'coupon_count_style_section',
            [
                'label' => __('Coupon Count Badge', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_coupon_count' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'badge_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'badge_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'badge_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-count-badge',
            ]
        );

        $this->add_control(
            'badge_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '50',
                    'right' => '50',
                    'bottom' => '50',
                    'left' => '50',
                    'unit' => '%',
                ],
            ]
        );

        $this->add_control(
            'badge_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '3',
                    'right' => '8',
                    'bottom' => '3',
                    'left' => '8',
                    'unit' => 'px',
                ],
            ]
        );

        $this->add_control(
            'badge_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '0',
                    'right' => '0',
                    'bottom' => '0',
                    'left' => '5',
                    'unit' => 'px',
                ],
            ]
        );

        $this->end_controls_section();

        // Pagination Style Section
        $this->start_controls_section(
            'pagination_style_section',
            [
                'label' => __('Pagination Style', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'pagination' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'pagination_alignment',
            [
                'label' => __('Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'center',
                'selectors' => [
                    '{{WRAPPER}} .store-pagination' => 'text-align: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_spacing',
            [
                'label' => __('Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 30,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-pagination' => 'margin-top: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        // Start tabs for Normal, Hover, Active states
        $this->start_controls_tabs('pagination_style_tabs');

        // Normal state tab
        $this->start_controls_tab(
            'pagination_style_normal',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#f8f9fa',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state tab
        $this->start_controls_tab(
            'pagination_style_hover',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_hover_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers:hover:not(.current)' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_hover_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#339af0',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers:hover:not(.current)' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Active state tab
        $this->start_controls_tab(
            'pagination_style_active',
            [
                'label' => __('Active', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_active_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers.current' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_active_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .page-numbers.current' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'pagination_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .page-numbers',
            ]
        );

        $this->add_control(
            'pagination_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .page-numbers' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '4',
                    'right' => '4',
                    'bottom' => '4',
                    'left' => '4',
                    'unit' => 'px',
                ],
            ]
        );

        $this->add_control(
            'pagination_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .page-numbers' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '8',
                    'right' => '12',
                    'bottom' => '8',
                    'left' => '12',
                    'unit' => 'px',
                ],
            ]
        );

        // Pagination Icons
        $this->add_control(
            'pagination_icons_heading',
            [
                'label' => __('Pagination Icons', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'prev_icon',
            [
                'label' => __('Previous Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-chevron-left',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'next_icon',
            [
                'label' => __('Next Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-chevron-right',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'pagination_display',
            [
                'label' => __('Display Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'numbers',
                'options' => [
                    'numbers' => __('Numbers', 'advance-coupon'),
                    'numbers_with_prev_next' => __('Numbers with Prev/Next', 'advance-coupon'),
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Get networks options for dropdown
     */
    private function get_networks_options() {
        global $wpdb;

        $options = [
            'all' => __('All Networks', 'advance-coupon'),
        ];

        // Get all unique network values from store meta
        $networks = $wpdb->get_col(
            "SELECT DISTINCT meta_value FROM {$wpdb->postmeta}
            WHERE meta_key = '_store_network'
            AND meta_value != ''
            ORDER BY meta_value ASC"
        );

        foreach ($networks as $network) {
            $options[$network] = $network;
        }

        return $options;
    }

    /**
     * Get cached store count
     */
    private function get_cached_store_count() {
        $count = get_transient('advance_coupon_store_count');
        if (false === $count) {
            $count = wp_count_posts('store')->publish;
            set_transient('advance_coupon_store_count', $count, 12 * HOUR_IN_SECONDS); // Cache for 12 hours
        }
        return $count;
    }

    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Generate a unique ID for this widget instance
        $widget_id = 'store-widget-' . $this->get_id();

        // Pagination setup
        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
        if (isset($_GET['store-page'])) {
            $paged = intval($_GET['store-page']);
        }

        // Determine query parameters based on settings
        if ($settings['show_all_stores'] === 'yes') {
            // Archive mode: Show all stores with pagination
            $posts_per_page = $settings['pagination'] === 'yes' ? intval($settings['stores_per_page']) : 10; // Default to 10 if pagination is off
        } else {
            // Normal mode: Use pagination or limit setting
            if ($settings['pagination'] === 'yes') {
                $posts_per_page = intval($settings['stores_per_page']);
            } else {
                // This is where we use the Number of Stores setting
                $posts_per_page = intval($settings['limit']);

                // Debug: Add a comment to show the actual value being used
                // echo '<!-- Using Number of Stores: ' . $posts_per_page . ' -->';
            }
        }

        // Query arguments
        $args = [
            'post_type' => 'store',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'paged' => $paged,
            'meta_query' => [],
        ];

        // For archive mode, we don't want to limit the total number of posts
        if ($settings['show_all_stores'] === 'yes') {
            // This ensures we get the correct pagination
            $args['no_found_rows'] = false;
        }

        // Filter by network
        if (!empty($settings['network']) && $settings['network'] !== 'all') {
            $args['meta_query'][] = [
                'key' => '_store_network',
                'value' => $settings['network'],
                'compare' => '=',
            ];
        }

        // Get stores using WP_Query for pagination
        $stores_query = new \WP_Query($args);
        $stores = $stores_query->posts;

        // Output
        // Create a container with data attributes for AJAX pagination
        echo '<div class="store-widget-container" id="' . esc_attr($widget_id) . '" data-widget-id="' . esc_attr($widget_id) . '" data-settings="' . esc_attr(json_encode($settings)) . '">';

        if (!empty($settings['title'])) {
            echo '<h3 class="store-widget-title">' . esc_html($settings['title']) . '</h3>';
        }

        if (!empty($stores)) {
            echo '<div class="store-list columns-' . esc_attr($settings['columns']) . '">';

            foreach ($stores as $store) {
                $store_url = get_post_meta($store->ID, '_store_url', true);
                $store_network = get_post_meta($store->ID, '_store_network', true);
                $thumbnail = get_the_post_thumbnail($store->ID, 'store-thumbnail');

                echo '<div class="store-item">';

                if ($thumbnail) {
                    echo '<div class="store-thumbnail">';
                    echo '<a href="' . get_permalink($store->ID) . '">' . $thumbnail . '</a>';
                    echo '</div>';
                }

                // Get coupon count for this store
                $coupon_count = 0;
                $coupon_args = array(
                    'post_type' => 'coupon',
                    'post_status' => 'publish',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => '_store_id',
                            'value' => $store->ID,
                            'compare' => '=',
                        ),
                    ),
                );
                $coupons = get_posts($coupon_args);
                $coupon_count = count($coupons);

                // Title and coupon count wrapper
                $wrapper_class = 'store-title-wrapper';
                if ($settings['show_coupon_count'] === 'yes' && $settings['badge_alignment'] === 'right') {
                    $wrapper_class .= ' badge-right-aligned';
                }

                echo '<div class="' . $wrapper_class . '">';
                echo '<h4 class="store-title"><a href="' . get_permalink($store->ID) . '">' . esc_html($store->post_title) . '</a></h4>';

                // Show coupon count badge if enabled
                if ($settings['show_coupon_count'] === 'yes') {
                    // Format the badge text
                    $badge_text = '';
                    if (!empty($settings['badge_text'])) {
                        $badge_text = sprintf($settings['badge_text'], $coupon_count);
                    } else {
                        $badge_text = $coupon_count;
                    }

                    echo '<span class="coupon-count-badge">' . $badge_text . '</span>';
                }

                echo '</div>'; // End of store-title-wrapper

                echo '<div class="store-actions">';
                echo '<a href="' . get_permalink($store->ID) . '" class="view-store">' . esc_html($settings['button_text']) . '</a>';
                echo '</div>';

                echo '</div>';
            }

            echo '</div>';
        } else {
            echo '<p>' . __('No stores found.', 'advance-coupon') . '</p>';
        }

        // Add pagination if enabled and there are multiple pages
        if ($settings['pagination'] === 'yes' && $stores_query->max_num_pages > 1) {
            echo '<div class="store-pagination">';

            $big = 999999999; // need an unlikely integer

            // Get previous and next icons
            $prev_icon = '';
            if (!empty($settings['prev_icon']['value'])) {
                ob_start();
                \Elementor\Icons_Manager::render_icon($settings['prev_icon'], ['aria-hidden' => 'true']);
                $prev_icon = ob_get_clean();
            } else {
                $prev_icon = '<i class="fas fa-chevron-left"></i>';
            }

            $next_icon = '';
            if (!empty($settings['next_icon']['value'])) {
                ob_start();
                \Elementor\Icons_Manager::render_icon($settings['next_icon'], ['aria-hidden' => 'true']);
                $next_icon = ob_get_clean();
            } else {
                $next_icon = '<i class="fas fa-chevron-right"></i>';
            }

            // Pagination args
            $pagination_args = array(
                'base' => add_query_arg('store-page', '%#%'),
                'format' => '?store-page=%#%',
                'current' => max(1, $paged),
                'total' => $stores_query->max_num_pages,
                'prev_text' => $prev_icon,
                'next_text' => $next_icon,
                'type' => 'list',
            );

            // If display style is numbers only, remove prev/next
            if ($settings['pagination_display'] === 'numbers') {
                $pagination_args['prev_next'] = false;
            }

            // Generate the pagination links
            echo paginate_links($pagination_args);

            echo '</div>';
        }

        echo '</div>'; // Close store-widget-container

        // CSS is now loaded from external file, but keeping inline styles as fallback
        // for backward compatibility
        ?>
        <style>
            /* Note: These styles are duplicated in the external CSS file.
             * In future versions, this inline CSS will be removed completely.
             */
            .store-widget-container {
                margin-bottom: 30px;
            }
            .store-list {
                display: grid;
                gap: 20px;
            }
            .store-list.columns-1 {
                grid-template-columns: repeat(1, 1fr);
            }
            .store-list.columns-2 {
                grid-template-columns: repeat(2, 1fr);
            }
            .store-list.columns-3 {
                grid-template-columns: repeat(3, 1fr);
            }
            .store-list.columns-4 {
                grid-template-columns: repeat(4, 1fr);
            }
            .store-list.columns-5 {
                grid-template-columns: repeat(5, 1fr);
            }
            .store-list.columns-6 {
                grid-template-columns: repeat(6, 1fr);
            }
            @media (max-width: 1024px) {
                .store-list.columns-5, .store-list.columns-6 {
                    grid-template-columns: repeat(4, 1fr);
                }
            }
            @media (max-width: 768px) {
                .store-list.columns-4, .store-list.columns-5, .store-list.columns-6 {
                    grid-template-columns: repeat(3, 1fr);
                }
            }
            @media (max-width: 767px) {
                .store-list.columns-3, .store-list.columns-4, .store-list.columns-5, .store-list.columns-6 {
                    grid-template-columns: repeat(2, 1fr);
                }
            }
            @media (max-width: 480px) {
                .store-list {
                    grid-template-columns: repeat(1, 1fr) !important;
                }
            }
            .store-item {
                padding: 20px;
                border: 1px solid #e5e5e5;
                border-radius: 5px;
                background-color: #fff;
                box-shadow: 0 2px 5px rgba(0,0,0,0.05);
                text-align: center;
            }
            .store-thumbnail {
                margin-bottom: 0;
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: #fff;
                border-radius: 5px;
                overflow: hidden;
                position: relative;
            }
            .store-thumbnail img {
                display: block;
                width: 100%;
                height: auto;
                border-radius: 5px;
                transition: transform 0.3s ease;
            }
            .store-thumbnail:hover img {
                transform: scale(1.05);
            }

            /* Title wrapper styles */
            .store-title-wrapper {
                display: flex;
                align-items: center;
                margin-top: 15px;
                flex-wrap: wrap;
                position: relative;
            }

            /* Right-aligned badge wrapper */
            .store-title-wrapper.badge-right-aligned {
                justify-content: space-between;
            }

            .store-title-wrapper.badge-right-aligned .store-title {
                margin-right: 10px;
            }

            /* Coupon count badge styles */
            .coupon-count-badge {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                background-color: #4dabf7;
                color: #fff;
                border-radius: 50px;
                font-size: 12px;
                font-weight: bold;
                padding: 3px 8px;
                margin-left: 5px;
                white-space: nowrap;
            }

            /* Make badge fit content width */
            .badge-right-aligned .coupon-count-badge {
                margin-left: auto;
            }
            .store-title {
                margin: 10px 0;
                font-size: 18px;
            }
            .store-title a {
                color: inherit;
                text-decoration: none;
            }
            .store-network {
                font-size: 14px;
                color: #666;
                margin-bottom: 15px;
            }
            .store-actions {
                display: flex;
                flex-direction: column;
                gap: 10px;
                margin-top: 15px;
            }
            .store-actions a {
                display: block;
                text-align: center;
                padding: 10px 15px;
                border-radius: 4px;
                text-decoration: none;
                font-weight: bold;
                transition: background-color 0.3s;
            }
            .view-store {
                background-color: #4dabf7;
                color: #fff;
            }
            .view-store:hover {
                background-color: #339af0;
                color: #fff;
            }
            .visit-store {
                background-color: #f8f9fa;
                color: #495057;
                border: 1px solid #dee2e6;
            }
            .visit-store:hover {
                background-color: #e9ecef;
                color: #212529;
            }
            /* Make store item position relative */
            .store-item {
                position: relative;
            }

            /* Improve view-store button */
            .view-store {
                width: 100%;
                box-sizing: border-box;
                display: inline-block;
                margin: 10px 0 0;
                text-decoration: none;
                text-align: center;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            /* Default hover styles (can be overridden by Elementor) */
            .view-store:hover {
                background-color: #339af0;
                color: #ffffff;
            }

            /* Pagination Styles */
            .store-pagination {
                margin-top: 30px;
                text-align: center;
            }

            .store-pagination .page-numbers {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                padding: 8px 12px;
                margin: 0 3px;
                background-color: #f8f9fa;
                color: #333;
                border-radius: 4px;
                text-decoration: none;
                transition: all 0.3s ease;
                min-width: 36px;
                height: 36px;
                box-sizing: border-box;
            }

            .store-pagination .page-numbers.current {
                background-color: #4dabf7;
                color: #fff;
                font-weight: bold;
            }

            .store-pagination .page-numbers:hover:not(.current) {
                background-color: #339af0;
                color: #fff;
            }

            .store-pagination .nav-links {
                display: inline-block;
            }

            .store-pagination ul {
                list-style: none;
                padding: 0;
                margin: 0;
                display: flex;
                justify-content: center;
                flex-wrap: wrap;
                background-color: transparent !important;
            }

            .store-pagination ul li {
                margin: 0 3px;
                display: flex;
                background-color: transparent !important;
            }

            .store-pagination ul li:hover {
                background-color: transparent !important;
            }

            /* Fix for icon alignment */
            .store-pagination .page-numbers i,
            .store-pagination .page-numbers svg {
                display: inline-flex;
                align-items: center;
                justify-content: center;
                width: 1em;
                height: 1em;
            }
        </style>
        <?php
    }
}
