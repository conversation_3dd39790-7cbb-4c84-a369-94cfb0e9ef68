/**
 * Store Coupons Widget JavaScript - Final Version
 *
 * Handles popup functionality and interactions for the Store Coupons Widget.
 * - Shows popup for coupon codes
 * - Opens affiliate links in new window
 * - <PERSON>les code copying
 *
 * Version 1.0.74 - Fixed issue with affiliate links opening twice (improved solution)
 * - Removed all debug console.log statements for production
 * - Implemented robust timer management with clearTimeout
 * - Added proper timer variable to track and clear setTimeout instances
 * - Enhanced openLink function with better error handling and validation
 * - Added checks to prevent any possibility of duplicate link opening
 * - Fixed race condition between click tracking and link opening
 *
 * Version 1.0.72 - Fixed issue with couponId not being passed to showPopup function
 * - Added couponId parameter to showPopup function call
 * - Added couponId parameter to showPopup function definition
 * - Added error handling for couponId in usage count AJAX call
 * - Added additional logging for debugging
 */
(function($) {
    'use strict';

    // Global variables
    var hasOpenedLink = false; // Track if link has been opened
    var linkTimer = null; // Timer for delayed link opening

    // Flag to track if widget has been initialized
    var widgetInitialized = false;

    // Initialize Store Coupons Widget
    function initStoreCouponsWidget() {
        // Remove any existing event handlers first to prevent duplicates
        $('.coupon-button').off('click');

        // Handle coupon button click
        $('.coupon-button').on('click', function(e) {
            e.preventDefault();

            // Reset the flag and clear any existing timers on each new button click
            hasOpenedLink = false;
            if (linkTimer) {
                clearTimeout(linkTimer);
                linkTimer = null;
            }

            var $button = $(this);
            var action = $button.data('action');
            var link = $button.data('link');
            var code = $button.data('code');
            var storeLogo = $button.data('store-logo');
            var storeName = $button.data('store-name');
            var couponTitle = $button.data('coupon-title');
            var couponId = $button.data('coupon-id');

            // Track the click first - this is the only place we track clicks
            trackCouponClick(couponId, function() {
                // If popup action (for online code)
                if (action === 'popup' && code) {
                    // For popup action, we'll show the popup and let the popup handle link opening with delay
                    showPopup(code, link, storeLogo, storeName, couponTitle, couponId);
                    // Link will be opened by the setTimeout inside showPopup, don't open it here
                } else {
                    // For non-code coupons, just open the link directly without popup
                    openLink(link);
                }
            });
        });
    }

    // Function to track coupon click via AJAX
    function trackCouponClick(couponId, callback) {
        if (!couponId) {
            if (callback) callback();
            return;
        }

        $.ajax({
            url: advance_coupon_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'track_coupon_click',
                coupon_id: couponId,
                nonce: advance_coupon_ajax.nonce
            },
            success: function() {
                // Success handling if needed
            },
            error: function() {
                // Error handling if needed
            },
            complete: function() {
                if (callback) callback();
            }
        });
    }

    /**
     * Function to show popup with coupon details
     *
     * @param {string} code - The coupon code to display and copy
     * @param {string} link - The affiliate link to open
     * @param {string} storeLogo - URL of the store logo
     * @param {string} storeName - Name of the store
     * @param {string} couponTitle - Title of the coupon
     * @param {string|number} couponId - ID of the coupon for tracking and usage count
     */
    function showPopup(code, link, storeLogo, storeName, couponTitle, couponId) {
        // Create overlay
        var $overlay = $('<div class="coupon-popup-overlay"></div>');

        // Clone the popup template
        var $popupTemplate = $('#coupon-popup-template');
        var $popup = $popupTemplate.clone();
        $popup.attr('id', '').removeAttr('style');

        // Fill popup content
        $popup.find('.popup-title').text(couponTitle);
        $popup.find('.popup-store-logo img').attr('src', storeLogo).attr('alt', storeName);
        $popup.find('.popup-code').text(code);

        // Get real usage count from meta
        var usageCount = 0;

        // Get usage count via AJAX
        // Check if couponId is defined before making the AJAX call
        if (couponId) {
            $.ajax({
                url: advance_coupon_ajax.ajax_url,
                type: 'POST',
                async: false, // Make synchronous to ensure we have the count before showing popup
                data: {
                    action: 'get_coupon_usage_count',
                    coupon_id: couponId,
                    nonce: advance_coupon_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        usageCount = response.data.used_count;
                    }
                },
                error: function() {
                    // Error handling if needed
                }
            });
        }

        // Update usage count in popup
        $popup.find('.popup-usage-count').html('Used by ' + usageCount);

        // Add to overlay and append to body
        $overlay.append($popup.html());
        $('body').append($overlay);

        // Prevent body scrolling
        $('body').addClass('popup-open').css('overflow', 'hidden');

        // Handle close button
        $('.popup-close').on('click', function() {
            closePopup();
        });

        // Close on click outside
        $overlay.on('click', function(e) {
            if (!$(e.target).closest('.coupon-popup').length) {
                closePopup();
            }
        });

        // Close with ESC key
        $(document).on('keyup.coupon-popup', function(e) {
            if (e.key === "Escape") {
                closePopup();
            }
        });

        // Automatically copy code to clipboard when popup opens
        copyToClipboard(code);

        // Show visual feedback that code was copied
        var $codeElement = $('.popup-code');
        $codeElement.addClass('copied');

        // Add a small delay before showing COPIED! text
        setTimeout(function() {
            $codeElement.text('COPIED!');

            // After 1.5 seconds, revert back to showing the code
            setTimeout(function() {
                $codeElement.text(code);
            }, 1500);
        }, 300);

        // Also handle manual click on code element
        $('.popup-code').on('click', function() {
            copyToClipboard(code);

            // Visual feedback
            var $this = $(this);
            $this.addClass('copied');
            $this.text('COPIED!');

            setTimeout(function() {
                $this.text(code);
            }, 1500);
        });

        // Open affiliate link in new window after a delay, but only for popup action
        // First, cancel any existing timers to prevent duplicate opening
        if (linkTimer) {
            clearTimeout(linkTimer);
            linkTimer = null;
        }

        // Set new timer for opening the link
        linkTimer = setTimeout(function() {
            // Make sure link hasn't been opened yet
            if (!hasOpenedLink) {
                openLink(link);
            }
            // Clear the timer reference after it's executed
            linkTimer = null;
        }, 1500);
    }

    /**
     * Function to open link - prevents duplicate opening
     * This is the ONLY place in the code where window.open should be called
     *
     * @param {string} link - The URL to open
     */
    function openLink(link) {
        // Don't proceed if the link has already been opened
        if (hasOpenedLink) {
            return;
        }

        // Don't proceed if the link is empty or invalid
        if (!link || typeof link !== 'string' || link.trim() === '') {
            return;
        }

        // Set flag first to prevent any possibility of duplicate opening
        hasOpenedLink = true;

        // Cancel any pending timers to prevent race conditions
        if (linkTimer) {
            clearTimeout(linkTimer);
            linkTimer = null;
        }

        try {
            // Use a direct window.open call with the _blank target
            var newWindow = window.open(link, '_blank');

            // Check if the window was successfully opened
            if (!newWindow || newWindow.closed || typeof newWindow.closed === 'undefined') {
                // Reset the flag if window.open failed
                hasOpenedLink = false;
            }
        } catch (e) {
            // Reset the flag if an error occurred
            hasOpenedLink = false;
        }
    }

    // Close popup function
    function closePopup() {
        $('.coupon-popup-overlay').remove();
        $('body').removeClass('popup-open').css('overflow', '');
        $(document).off('keyup.coupon-popup');
    }

    // Copy text to clipboard using modern Clipboard API with fallback
    function copyToClipboard(text) {
        // Try to use the modern Clipboard API first
        if (navigator.clipboard && window.isSecureContext) {
            // Modern API - works in secure contexts (HTTPS)
            navigator.clipboard.writeText(text).then(function() {
                // Success
            }, function() {
                // Fall back to the older method if modern API fails
                copyToClipboardFallback(text);
            });
        } else {
            // Fall back to the older method for non-secure contexts or older browsers
            copyToClipboardFallback(text);
        }
    }

    // Fallback method for copying to clipboard
    function copyToClipboardFallback(text) {
        // Create temporary element
        var $temp = $('<input>');
        $('body').append($temp);
        $temp.val(text).select();

        try {
            // Execute copy command
            document.execCommand('copy');
        } catch (err) {
            // Error handling if needed
        }

        // Remove temporary element
        $temp.remove();
    }

    // Initialize when document is ready
    $(document).ready(function() {
        // Set the flag to true and initialize
        if (!widgetInitialized) {
            widgetInitialized = true;
            initStoreCouponsWidget();
        }

        // Also initialize when Elementor frontend is initialized (for Elementor editor)
        // But remove any previous handlers first
        $(document).off('elementor/frontend/init.storeCoupons').on('elementor/frontend/init.storeCoupons', function() {
            if (typeof elementorFrontend !== 'undefined') {
                // Remove any existing hooks first
                if (elementorFrontend.hooks.getActions('frontend/element_ready/store_coupons_widget.default').length > 0) {
                    elementorFrontend.hooks.removeAction('frontend/element_ready/store_coupons_widget.default');
                }

                elementorFrontend.hooks.addAction('frontend/element_ready/store_coupons_widget.default', function() {
                    // Always reinitialize for Elementor to ensure proper event binding
                    initStoreCouponsWidget();
                });
            }
        });
    });

})(jQuery);
