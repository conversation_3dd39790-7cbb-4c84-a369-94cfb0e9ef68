/**
 * Store Description Widget Styles
 *
 * This file contains all styles for the store description widget.
 * Following WordPress best practices for CSS organization.
 */

/* Main wrapper with flex layout */
.store-description-wrapper {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    gap: 30px;
}

/* Thumbnail container */
.store-description-thumbnail {
    flex: 0 0 250px;
    max-width: 250px;
}

/* Responsive layout for small screens */
@media (max-width: 767px) {
    .store-description-wrapper {
        flex-direction: column;
    }

    .store-description-thumbnail {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }
}

/* Thumbnail image */
.store-description-thumbnail img {
    width: 100%;
    height: auto;
    display: block;
    border-radius: 5px;
}

/* Content wrapper */
.store-description-content-wrapper {
    flex: 1;
    min-width: 0; /* Prevents content from overflowing */
}

/* Store title */
.store-description-title {
    margin: 0 0 15px;
    font-size: 24px;
    font-weight: bold;
    color: #333;
}

/* Store description content */
.store-description-content {
    margin-bottom: 20px;
    color: #666;
    line-height: 1.6;
}

/* Read more link */
.store-description-read-more {
    display: inline;
    color: #4dabf7;
    font-weight: bold;
    text-decoration: none;
    cursor: pointer;
    white-space: nowrap;
}

.store-description-read-more:hover {
    color: #339af0;
    text-decoration: underline;
}

/* Actions row - contains button and social share */
.store-description-actions-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    flex-wrap: wrap;
    gap: 15px;
}

/* Button wrapper */
.store-description-button-wrapper {
    flex: 0 0 auto;
}

/* Button styles */
.store-description-button {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4dabf7;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.store-description-button:hover {
    background-color: #339af0;
    color: #fff;
    text-decoration: none;
}

/* Social share section */
.store-description-social-share {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    flex: 0 0 auto;
}

/* Default layout - Button left, Share right */
.actions-layout-button-left .store-description-button-wrapper {
    order: 1;
}

.actions-layout-button-left .store-description-social-share {
    order: 2;
    margin-left: auto; /* Push to right */
}

/* Button right, Share left */
.actions-layout-button-right .store-description-button-wrapper {
    order: 2;
    margin-left: auto; /* Push to right */
}

.actions-layout-button-right .store-description-social-share {
    order: 1;
}

/* Full width layout */
.actions-layout-full-width .store-description-button-wrapper,
.actions-layout-full-width .store-description-social-share {
    flex: 1 1 45%;
}

/* Column direction (vertical) */
.store-description-actions-row[style*="flex-direction: column"] {
    align-items: flex-start;
}

.store-description-actions-row[style*="flex-direction: column"] .store-description-button-wrapper,
.store-description-actions-row[style*="flex-direction: column"] .store-description-social-share {
    width: 100%;
    margin-left: 0;
    margin-bottom: 15px;
}

/* Responsive adjustments */
@media (max-width: 767px) {
    .store-description-actions-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .store-description-button-wrapper,
    .store-description-social-share {
        width: 100%;
        margin-left: 0;
        margin-bottom: 15px;
    }

    /* Override order for mobile */
    .actions-layout-button-left .store-description-button-wrapper,
    .actions-layout-button-right .store-description-button-wrapper {
        order: 1;
    }

    .actions-layout-button-left .store-description-social-share,
    .actions-layout-button-right .store-description-social-share {
        order: 2;
    }
}

.store-description-social-share-title {
    display: inline-block;
    margin-right: 10px;
    font-weight: bold;
    color: #666;
}

.store-description-social-share a {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    text-decoration: none;
    transition: transform 0.3s ease;
    width: 30px;
    height: 30px;
}

.store-description-social-share a:hover {
    transform: scale(1.2);
}

.store-description-social-share i {
    font-family: 'FontAwesome';
    font-size: 24px;
    vertical-align: middle;
    line-height: 1;
}

/* Social icon colors */
.social-icon-facebook i {
    color: #3b5998;
}

.social-icon-instagram i {
    color: #e1306c;
}

.social-icon-whatsapp i {
    color: #25d366;
}

.social-icon-tiktok i {
    color: #000000;
}

/* Hover colors */
.social-icon-facebook:hover i {
    color: #2d4373;
}

.social-icon-instagram:hover i {
    color: #c13584;
}

.social-icon-whatsapp:hover i {
    color: #128c7e;
}

.social-icon-tiktok:hover i {
    color: #69c9d0;
}
