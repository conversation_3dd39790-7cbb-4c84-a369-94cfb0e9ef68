<?php
namespace AdvanceCoupon\Elementor\Widgets;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Related Stores Widget for Elementor
 *
 * This widget displays related stores in a carousel format for a single store page.
 * Stores are related by categories and displayed randomly.
 */
class Related_Stores_Widget extends \Elementor\Widget_Base {
    /**
     * Get widget name
     */
    public function get_name() {
        return 'related_stores_widget';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return __('Related Stores', 'advance-coupon');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-carousel';
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['theme-elements', 'general'];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['store', 'related', 'carousel', 'slider'];
    }

    /**
     * Get current store ID
     */
    protected function get_current_store_id() {
        // Check if we're on a single store page
        if (is_singular('store')) {
            return get_the_ID();
        }

        return false;
    }

    /**
     * Get stores options for dropdown
     */
    protected function get_stores_options() {
        $options = [
            'current' => __('Current Store', 'advance-coupon'),
        ];

        $stores = get_posts([
            'post_type' => 'store',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
        ]);

        foreach ($stores as $store) {
            $options[$store->ID] = $store->post_title;
        }

        return $options;
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'store_id',
            [
                'label' => __('Store', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'options' => $this->get_stores_options(),
                'default' => 'current',
                'label_block' => true,
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => __('Widget Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Related Stores', 'advance-coupon'),
                'label_block' => true,
            ]
        );

        $this->add_control(
            'number_of_stores',
            [
                'label' => __('Number of Stores', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 20,
                'step' => 1,
                'default' => 6,
            ]
        );

        $this->add_control(
            'slides_to_show',
            [
                'label' => __('Slides to Show', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 10,
                'step' => 1,
                'default' => 3,
            ]
        );

        $this->add_control(
            'slides_to_scroll',
            [
                'label' => __('Slides to Scroll', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1,
                'max' => 10,
                'step' => 1,
                'default' => 1,
            ]
        );

        $this->add_control(
            'autoplay',
            [
                'label' => __('Autoplay', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'autoplay_speed',
            [
                'label' => __('Autoplay Speed', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 1000,
                'max' => 10000,
                'step' => 500,
                'default' => 3000,
                'condition' => [
                    'autoplay' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'infinite',
            [
                'label' => __('Infinite Loop', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_arrows',
            [
                'label' => __('Show Arrows', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_dots',
            [
                'label' => __('Show Dots', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'button_text',
            [
                'label' => __('Button Text', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('View Store', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'show_coupon_count',
            [
                'label' => __('Show Coupon Count', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'badge_text',
            [
                'label' => __('Badge Text Format', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('%d Coupons', 'advance-coupon'),
                'description' => __('Use %d as a placeholder for the number of coupons', 'advance-coupon'),
                'condition' => [
                    'show_coupon_count' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Carousel
        $this->start_controls_section(
            'carousel_style_section',
            [
                'label' => __('Carousel', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'arrow_color',
            [
                'label' => __('Arrow Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .related-stores-carousel .slick-arrow i' => 'color: {{VALUE}} !important;',
                ],
                'condition' => [
                    'show_arrows' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'arrow_bg_color',
            [
                'label' => __('Arrow Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .related-stores-carousel .slick-arrow' => 'background-color: {{VALUE}} !important;',
                ],
                'condition' => [
                    'show_arrows' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'arrow_size',
            [
                'label' => __('Arrow Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 20,
                ],
                'selectors' => [
                    '{{WRAPPER}} .related-stores-carousel .slick-arrow i' => 'font-size: {{SIZE}}{{UNIT}} !important;',
                ],
                'condition' => [
                    'show_arrows' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'dot_color',
            [
                'label' => __('Dot Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .related-stores-carousel .slick-dots li button' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'show_dots' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'active_dot_color',
            [
                'label' => __('Active Dot Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .related-stores-carousel .slick-dots li.slick-active button' => 'background-color: {{VALUE}};',
                ],
                'condition' => [
                    'show_dots' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Store Items
        $this->start_controls_section(
            'store_style_section',
            [
                'label' => __('Store Items', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'store_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'store_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-item',
            ]
        );

        $this->add_control(
            'store_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'store_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-item',
            ]
        );

        $this->add_responsive_control(
            'store_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-item' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Store Title
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __('Store Title', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-title a' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-title',
            ]
        );

        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Button
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => __('Button', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .view-store',
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .view-store',
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'button_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .view-store' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Coupon Count Badge
        $this->start_controls_section(
            'badge_style_section',
            [
                'label' => __('Coupon Count Badge', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_coupon_count' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'badge_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'badge_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'badge_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-count-badge',
            ]
        );

        $this->add_control(
            'badge_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'badge_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-count-badge' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Get store ID
        $store_id = $settings['store_id'];
        if ($store_id === 'current') {
            $store_id = $this->get_current_store_id();
            if (!$store_id) {
                echo '<div class="related-stores-error">' . __('No store found.', 'advance-coupon') . '</div>';
                return;
            }
        }

        // Get store categories
        $store_categories = wp_get_object_terms($store_id, 'store_category', array('fields' => 'ids'));

        if (empty($store_categories) || is_wp_error($store_categories)) {
            echo '<div class="related-stores-error">' . __('No categories found for this store.', 'advance-coupon') . '</div>';
            return;
        }

        // Query for related stores
        $args = array(
            'post_type' => 'store',
            'post_status' => 'publish',
            'posts_per_page' => $settings['number_of_stores'],
            'post__not_in' => array($store_id), // Exclude current store
            'orderby' => 'rand', // Random order
            'tax_query' => array(
                array(
                    'taxonomy' => 'store_category',
                    'field' => 'term_id',
                    'terms' => $store_categories,
                ),
            ),
        );

        $related_stores = new \WP_Query($args);

        if (!$related_stores->have_posts()) {
            echo '<div class="related-stores-error">' . __('No related stores found.', 'advance-coupon') . '</div>';
            return;
        }

        // Generate a unique ID for this widget instance
        $widget_id = 'related-stores-' . $this->get_id();

        // Output
        echo '<div class="related-stores-widget" id="' . esc_attr($widget_id) . '">';

        if (!empty($settings['title'])) {
            echo '<h3 class="related-stores-title">' . esc_html($settings['title']) . '</h3>';
        }

        echo '<div class="related-stores-carousel" data-slides-to-show="' . esc_attr($settings['slides_to_show']) . '" data-slides-to-scroll="' . esc_attr($settings['slides_to_scroll']) . '" data-autoplay="' . esc_attr($settings['autoplay']) . '" data-autoplay-speed="' . esc_attr($settings['autoplay_speed']) . '" data-infinite="' . esc_attr($settings['infinite']) . '" data-show-arrows="' . esc_attr($settings['show_arrows']) . '" data-show-dots="' . esc_attr($settings['show_dots']) . '">';

        while ($related_stores->have_posts()) {
            $related_stores->the_post();
            $store_url = get_post_meta(get_the_ID(), '_store_url', true);
            $store_network = get_post_meta(get_the_ID(), '_store_network', true);
            $thumbnail = get_the_post_thumbnail(get_the_ID(), 'store-thumbnail');

            echo '<div class="store-item">';

            if ($thumbnail) {
                echo '<div class="store-thumbnail">';
                echo '<a href="' . get_permalink() . '">' . $thumbnail . '</a>';
                echo '</div>';
            }

            // Get coupon count for this store
            $coupon_count = 0;
            $coupon_args = array(
                'post_type' => 'coupon',
                'post_status' => 'publish',
                'posts_per_page' => -1,
                'meta_query' => array(
                    array(
                        'key' => '_store_id',
                        'value' => get_the_ID(),
                        'compare' => '=',
                    ),
                ),
            );

            $coupon_query = new \WP_Query($coupon_args);
            $coupon_count = $coupon_query->found_posts;

            // Determine wrapper class based on coupon count visibility
            $wrapper_class = 'store-title-wrapper';
            if ($settings['show_coupon_count'] === 'yes') {
                $wrapper_class .= ' has-badge';
            }

            echo '<div class="' . $wrapper_class . '">';
            echo '<h4 class="store-title"><a href="' . get_permalink() . '">' . get_the_title() . '</a></h4>';

            // Show coupon count badge if enabled
            if ($settings['show_coupon_count'] === 'yes') {
                // Format the badge text
                $badge_text = '';
                if (!empty($settings['badge_text'])) {
                    $badge_text = sprintf($settings['badge_text'], $coupon_count);
                } else {
                    $badge_text = $coupon_count;
                }

                echo '<span class="coupon-count-badge">' . $badge_text . '</span>';
            }

            echo '</div>'; // End of store-title-wrapper

            echo '<div class="store-actions">';
            echo '<a href="' . get_permalink() . '" class="view-store">' . esc_html($settings['button_text']) . '</a>';
            echo '</div>';

            echo '</div>'; // End of store-item
        }

        echo '</div>'; // End of related-stores-carousel
        echo '</div>'; // End of related-stores-widget

        wp_reset_postdata();

        // Enqueue Slick Slider
        wp_enqueue_style('slick', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css', array(), '1.8.1');
        wp_enqueue_style('slick-theme', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css', array(), '1.8.1');
        wp_enqueue_script('slick', 'https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js', array('jquery'), '1.8.1', true);

        // Enqueue Font Awesome for icons - use both 5.x and 6.x to ensure compatibility
        wp_enqueue_style(
            'font-awesome-5',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4'
        );

        // Also enqueue Font Awesome 6 for better compatibility
        wp_enqueue_style(
            'font-awesome-6',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css',
            array(),
            '6.4.0'
        );

        // Enqueue our custom styles with higher priority to override Slick theme
        wp_enqueue_style(
            'advance-coupon-related-stores-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/related-stores-widget.css',
            array('slick', 'slick-theme', 'font-awesome-5'),
            ADVCOUPON_VERSION
        );

        // Inline script to initialize the carousel
        ?>
        <script>
            jQuery(document).ready(function($) {
                initRelatedStoresCarousel();

                function initRelatedStoresCarousel() {
                    var $carousel = $('#<?php echo esc_js($widget_id); ?> .related-stores-carousel');

                    // Debug output
                    console.log('Initializing carousel:', $carousel);
                    console.log('Show arrows setting:', <?php echo $settings['show_arrows'] === 'yes' ? 'true' : 'false'; ?>);

                    if ($carousel.length && !$carousel.hasClass('slick-initialized')) {
                        $carousel.slick({
                            slidesToShow: <?php echo esc_js($settings['slides_to_show']); ?>,
                            slidesToScroll: <?php echo esc_js($settings['slides_to_scroll']); ?>,
                            autoplay: <?php echo $settings['autoplay'] === 'yes' ? 'true' : 'false'; ?>,
                            autoplaySpeed: <?php echo esc_js($settings['autoplay_speed']); ?>,
                            infinite: <?php echo $settings['infinite'] === 'yes' ? 'true' : 'false'; ?>,
                            arrows: <?php echo $settings['show_arrows'] === 'yes' ? 'true' : 'false'; ?>,
                            dots: <?php echo $settings['show_dots'] === 'yes' ? 'true' : 'false'; ?>,
                            speed: 600,
                            cssEase: 'cubic-bezier(0.25, 0.46, 0.45, 0.94)',
                            centerPadding: '30px',
                            adaptiveHeight: true,
                            prevArrow: '<button type="button" class="slick-prev"><i class="fas fa-chevron-left" aria-hidden="true"></i><span class="screen-reader-text">Previous</span></button>',
                            nextArrow: '<button type="button" class="slick-next"><i class="fas fa-chevron-right" aria-hidden="true"></i><span class="screen-reader-text">Next</span></button>',
                            customPaging: function(slider, i) {
                                return '<button type="button" aria-label="Go to slide ' + (i + 1) + '"></button>';
                            },
                            responsive: [
                                {
                                    breakpoint: 1200,
                                    settings: {
                                        slidesToShow: Math.min(<?php echo esc_js($settings['slides_to_show']); ?>, 4),
                                        slidesToScroll: 1,
                                        centerPadding: '20px'
                                    }
                                },
                                {
                                    breakpoint: 1024,
                                    settings: {
                                        slidesToShow: Math.min(<?php echo esc_js($settings['slides_to_show']); ?>, 3),
                                        slidesToScroll: 1,
                                        centerPadding: '15px'
                                    }
                                },
                                {
                                    breakpoint: 768,
                                    settings: {
                                        slidesToShow: Math.min(<?php echo esc_js($settings['slides_to_show']); ?>, 2),
                                        slidesToScroll: 1,
                                        centerPadding: '10px',
                                        arrows: true,
                                        dots: true
                                    }
                                },
                                {
                                    breakpoint: 480,
                                    settings: {
                                        slidesToShow: 1,
                                        slidesToScroll: 1,
                                        centerPadding: '0px',
                                        arrows: true,
                                        dots: true
                                    }
                                }
                            ]
                        });
                    }
                }

                // Also initialize when Elementor frontend is initialized (for Elementor editor)
                $(document).on('elementor/frontend/init', function() {
                    if (typeof elementorFrontend !== 'undefined') {
                        elementorFrontend.hooks.addAction('frontend/element_ready/related_stores_widget.default', function() {
                            initRelatedStoresCarousel();
                        });
                    }
                });
            });
        </script>
        <?php
    }
}
