<?php
/**
 * Category Meta Fields for Advance Coupon
 * 
 * This class handles adding image and icon fields to store categories
 */
class Advance_Coupon_Category_Meta {
    
    /**
     * Holds the instance of this class
     */
    private static $instance = null;
    
    /**
     * Get the instance of this class
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    public function __construct() {
        // Add form fields
        add_action('store_category_add_form_fields', array($this, 'add_category_fields'), 10, 2);
        add_action('store_category_edit_form_fields', array($this, 'edit_category_fields'), 10, 2);
        
        // Save form fields
        add_action('created_store_category', array($this, 'save_category_fields'), 10, 2);
        add_action('edited_store_category', array($this, 'save_category_fields'), 10, 2);
        
        // Add admin scripts
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }
    
    /**
     * Enqueue admin scripts
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on category pages
        if ('edit-tags.php' !== $hook && 'term.php' !== $hook) {
            return;
        }
        
        // Check if we're on the store_category taxonomy
        $screen = get_current_screen();
        if (!$screen || 'store_category' !== $screen->taxonomy) {
            return;
        }
        
        // Enqueue media scripts
        wp_enqueue_media();
        
        // Enqueue font awesome for icon picker
        wp_enqueue_style('font-awesome', 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css', array(), '5.15.4');
        
        // Enqueue our custom script
        wp_enqueue_script('advance-coupon-category-meta', ADVCOUPON_PLUGIN_URL . 'assets/js/category-meta.js', array('jquery'), ADVCOUPON_VERSION, true);
    }
    
    /**
     * Add category fields
     */
    public function add_category_fields($taxonomy) {
        ?>
        <div class="form-field term-image-wrap">
            <label for="category-image"><?php _e('Category Image', 'advance-coupon'); ?></label>
            <div class="category-image-container">
                <img src="<?php echo esc_url(ADVCOUPON_PLUGIN_URL . 'assets/images/placeholder.png'); ?>" class="category-image-preview" style="max-width: 200px; height: auto;">
                <input type="hidden" name="category_image" id="category-image" value="">
                <button type="button" class="button button-secondary category-image-upload"><?php _e('Upload Image', 'advance-coupon'); ?></button>
                <button type="button" class="button button-secondary category-image-remove" style="display:none;"><?php _e('Remove Image', 'advance-coupon'); ?></button>
            </div>
            <p class="description"><?php _e('Upload an image for this category.', 'advance-coupon'); ?></p>
        </div>
        
        <div class="form-field term-icon-wrap">
            <label for="category-icon"><?php _e('Category Icon', 'advance-coupon'); ?></label>
            <div class="category-icon-container">
                <input type="text" name="category_icon" id="category-icon" value="" class="category-icon-input">
                <button type="button" class="button button-secondary category-icon-select"><?php _e('Select Icon', 'advance-coupon'); ?></button>
                <div class="category-icon-preview" style="margin-top: 10px; font-size: 2em;">
                    <i class=""></i>
                </div>
            </div>
            <p class="description"><?php _e('Select a Font Awesome icon for this category. Used if no image is uploaded.', 'advance-coupon'); ?></p>
            <div class="category-icon-picker" style="display:none; margin-top: 10px; max-height: 200px; overflow-y: scroll; padding: 10px; border: 1px solid #ddd;">
                <div class="icon-search">
                    <input type="text" placeholder="<?php _e('Search icons...', 'advance-coupon'); ?>" class="icon-search-input">
                </div>
                <div class="icon-list" style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 10px; margin-top: 10px;">
                    <?php
                    // Common Font Awesome icons
                    $icons = array(
                        'fas fa-tag', 'fas fa-tags', 'fas fa-shopping-bag', 'fas fa-shopping-cart', 'fas fa-store',
                        'fas fa-gift', 'fas fa-percentage', 'fas fa-dollar-sign', 'fas fa-money-bill-wave',
                        'fas fa-credit-card', 'fas fa-wallet', 'fas fa-coins', 'fas fa-piggy-bank', 'fas fa-cash-register',
                        'fas fa-receipt', 'fas fa-ticket-alt', 'fas fa-certificate', 'fas fa-award', 'fas fa-medal',
                        'fas fa-trophy', 'fas fa-star', 'fas fa-heart', 'fas fa-thumbs-up', 'fas fa-check',
                        'fas fa-check-circle', 'fas fa-bullhorn', 'fas fa-bell', 'fas fa-calendar', 'fas fa-calendar-alt',
                        'fas fa-clock', 'fas fa-hourglass', 'fas fa-sync', 'fas fa-redo', 'fas fa-history',
                        'fas fa-box', 'fas fa-boxes', 'fas fa-archive', 'fas fa-truck', 'fas fa-shipping-fast',
                        'fas fa-map-marker-alt', 'fas fa-location-arrow', 'fas fa-compass', 'fas fa-globe',
                        'fas fa-search', 'fas fa-filter', 'fas fa-sort', 'fas fa-list', 'fas fa-th',
                        'fas fa-th-large', 'fas fa-user', 'fas fa-users', 'fas fa-user-circle', 'fas fa-id-card',
                        'fas fa-address-card', 'fas fa-envelope', 'fas fa-phone', 'fas fa-mobile-alt', 'fas fa-comment',
                        'fas fa-comments', 'fas fa-question-circle', 'fas fa-info-circle', 'fas fa-exclamation-circle'
                    );
                    
                    foreach ($icons as $icon) {
                        echo '<div class="icon-item" data-icon="' . esc_attr($icon) . '"><i class="' . esc_attr($icon) . '"></i></div>';
                    }
                    ?>
                </div>
            </div>
        </div>
        <?php
    }
    
    /**
     * Edit category fields
     */
    public function edit_category_fields($term, $taxonomy) {
        // Get term meta
        $image_id = get_term_meta($term->term_id, 'category_image', true);
        $icon = get_term_meta($term->term_id, 'category_icon', true);
        
        // Get image URL
        $image_url = $image_id ? wp_get_attachment_image_url($image_id, 'medium') : ADVCOUPON_PLUGIN_URL . 'assets/images/placeholder.png';
        ?>
        <tr class="form-field term-image-wrap">
            <th scope="row"><label for="category-image"><?php _e('Category Image', 'advance-coupon'); ?></label></th>
            <td>
                <div class="category-image-container">
                    <img src="<?php echo esc_url($image_url); ?>" class="category-image-preview" style="max-width: 200px; height: auto;">
                    <input type="hidden" name="category_image" id="category-image" value="<?php echo esc_attr($image_id); ?>">
                    <button type="button" class="button button-secondary category-image-upload"><?php _e('Upload Image', 'advance-coupon'); ?></button>
                    <button type="button" class="button button-secondary category-image-remove" <?php echo $image_id ? '' : 'style="display:none;"'; ?>><?php _e('Remove Image', 'advance-coupon'); ?></button>
                </div>
                <p class="description"><?php _e('Upload an image for this category.', 'advance-coupon'); ?></p>
            </td>
        </tr>
        
        <tr class="form-field term-icon-wrap">
            <th scope="row"><label for="category-icon"><?php _e('Category Icon', 'advance-coupon'); ?></label></th>
            <td>
                <div class="category-icon-container">
                    <input type="text" name="category_icon" id="category-icon" value="<?php echo esc_attr($icon); ?>" class="category-icon-input">
                    <button type="button" class="button button-secondary category-icon-select"><?php _e('Select Icon', 'advance-coupon'); ?></button>
                    <div class="category-icon-preview" style="margin-top: 10px; font-size: 2em;">
                        <i class="<?php echo esc_attr($icon); ?>"></i>
                    </div>
                </div>
                <p class="description"><?php _e('Select a Font Awesome icon for this category. Used if no image is uploaded.', 'advance-coupon'); ?></p>
                <div class="category-icon-picker" style="display:none; margin-top: 10px; max-height: 200px; overflow-y: scroll; padding: 10px; border: 1px solid #ddd;">
                    <div class="icon-search">
                        <input type="text" placeholder="<?php _e('Search icons...', 'advance-coupon'); ?>" class="icon-search-input">
                    </div>
                    <div class="icon-list" style="display: grid; grid-template-columns: repeat(8, 1fr); gap: 10px; margin-top: 10px;">
                        <?php
                        // Common Font Awesome icons
                        $icons = array(
                            'fas fa-tag', 'fas fa-tags', 'fas fa-shopping-bag', 'fas fa-shopping-cart', 'fas fa-store',
                            'fas fa-gift', 'fas fa-percentage', 'fas fa-dollar-sign', 'fas fa-money-bill-wave',
                            'fas fa-credit-card', 'fas fa-wallet', 'fas fa-coins', 'fas fa-piggy-bank', 'fas fa-cash-register',
                            'fas fa-receipt', 'fas fa-ticket-alt', 'fas fa-certificate', 'fas fa-award', 'fas fa-medal',
                            'fas fa-trophy', 'fas fa-star', 'fas fa-heart', 'fas fa-thumbs-up', 'fas fa-check',
                            'fas fa-check-circle', 'fas fa-bullhorn', 'fas fa-bell', 'fas fa-calendar', 'fas fa-calendar-alt',
                            'fas fa-clock', 'fas fa-hourglass', 'fas fa-sync', 'fas fa-redo', 'fas fa-history',
                            'fas fa-box', 'fas fa-boxes', 'fas fa-archive', 'fas fa-truck', 'fas fa-shipping-fast',
                            'fas fa-map-marker-alt', 'fas fa-location-arrow', 'fas fa-compass', 'fas fa-globe',
                            'fas fa-search', 'fas fa-filter', 'fas fa-sort', 'fas fa-list', 'fas fa-th',
                            'fas fa-th-large', 'fas fa-user', 'fas fa-users', 'fas fa-user-circle', 'fas fa-id-card',
                            'fas fa-address-card', 'fas fa-envelope', 'fas fa-phone', 'fas fa-mobile-alt', 'fas fa-comment',
                            'fas fa-comments', 'fas fa-question-circle', 'fas fa-info-circle', 'fas fa-exclamation-circle'
                        );
                        
                        foreach ($icons as $icon) {
                            echo '<div class="icon-item" data-icon="' . esc_attr($icon) . '"><i class="' . esc_attr($icon) . '"></i></div>';
                        }
                        ?>
                    </div>
                </div>
            </td>
        </tr>
        <?php
    }
    
    /**
     * Save category fields
     */
    public function save_category_fields($term_id, $tt_id) {
        if (isset($_POST['category_image'])) {
            update_term_meta($term_id, 'category_image', absint($_POST['category_image']));
        }
        
        if (isset($_POST['category_icon'])) {
            update_term_meta($term_id, 'category_icon', sanitize_text_field($_POST['category_icon']));
        }
    }
}

// Initialize the class
function initialize_advance_coupon_category_meta() {
    Advance_Coupon_Category_Meta::instance();
}
add_action('init', 'initialize_advance_coupon_category_meta', 5);
