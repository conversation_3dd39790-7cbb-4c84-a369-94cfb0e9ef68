<?php
/**
 * Elementor Template Override for Advance Coupon
 *
 * This class ensures Elementor templates are properly applied to custom post types
 */
class Elementor_Template_Override {

    /**
     * Holds the instance of this class
     */
    private static $instance = null;

    /**
     * Get the instance of this class
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        // Register the store post type with Elementor
        add_action('elementor/theme/register_locations', array($this, 'register_elementor_locations'));

        // Add the store post type to Elementor's supported post types
        add_filter('elementor/theme/get_public_post_types', array($this, 'add_cpt_support'));

        // Add the store_category taxonomy to Elementor's supported taxonomies
        add_filter('elementor/theme/get_public_taxonomies', array($this, 'add_taxonomy_support'));

        // Override the template for single store pages - CRITICAL PART
        add_filter('template_include', array($this, 'override_template'), 999999);

        // Force Elementor to recognize our templates
        add_filter('elementor/theme/need_override_location', array($this, 'force_template_override'), 999999, 2);
    }

    /**
     * Register Elementor locations
     */
    public function register_elementor_locations($locations_manager) {
        // Register the single store location
        $locations_manager->register_core_location('single');
        $locations_manager->register_core_location('archive');
    }

    /**
     * Add custom post type support to Elementor
     */
    public function add_cpt_support($post_types) {
        $post_types['store'] = get_post_type_object('store');
        return $post_types;
    }

    /**
     * Add taxonomy support to Elementor
     */
    public function add_taxonomy_support($taxonomies) {
        $taxonomies['store_category'] = get_taxonomy('store_category');
        return $taxonomies;
    }

    /**
     * Override the template for single store pages
     */
    public function override_template($template) {
        // Only apply to single store pages
        if (!is_singular('store')) {
            return $template;
        }

        // Check if Elementor Pro is active
        if (!class_exists('\ElementorPro\Plugin')) {
            return $template;
        }

        // Get the theme builder module
        $theme_builder = \ElementorPro\Modules\ThemeBuilder\Module::instance();
        $locations_manager = $theme_builder->get_locations_manager();

        // Check if we have a template for this location
        $documents = $theme_builder->get_conditions_manager()->get_documents_for_location('single');

        if (!empty($documents)) {
            // We have a template, let Elementor handle it
            add_action('template_redirect', function() use ($locations_manager) {
                $locations_manager->do_location('single');
            }, 11);

            // Try different template paths based on Elementor Pro version
            $possible_paths = array(
                ELEMENTOR_PRO_PATH . 'modules/theme-builder/views/theme-support-header-footer.php',
                ELEMENTOR_PRO_PATH . 'modules/theme-builder/views/theme-builder-header-footer.php',
                ELEMENTOR_PRO_PATH . 'modules/theme-builder/views/default.php'
            );

            foreach ($possible_paths as $path) {
                if (file_exists($path)) {
                    return $path;
                }
            }
        }

        return $template;
    }

    /**
     * Force Elementor to recognize our templates
     */
    public function force_template_override($override, $location) {
        if ($location === 'single' && is_singular('store')) {
            return true;
        }

        if ($location === 'archive' && (is_post_type_archive('store') || is_tax('store_category'))) {
            return true;
        }

        return $override;
    }

    /**
     * Get the template ID for a specific location and post type
     */
    private function get_template_id($location, $post_type) {
        // Check if Elementor Pro is active
        if (!class_exists('\ElementorPro\Plugin')) {
            return false;
        }

        // Get the theme builder module
        $theme_builder = \ElementorPro\Modules\ThemeBuilder\Module::instance();

        // Get the locations manager
        $locations_manager = $theme_builder->get_locations_manager();

        // Get documents for this location
        $documents = $theme_builder->get_conditions_manager()->get_documents_for_location($location);

        if (empty($documents)) {
            return false;
        }

        // Return the first document ID
        return array_key_first($documents);
    }
}

// Initialize the class
add_action('elementor/init', function() {
    Elementor_Template_Override::instance();
});
