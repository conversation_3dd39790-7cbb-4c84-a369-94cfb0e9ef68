/**
 * Styles for Store Widget
 */

/* Widget container */
.store-widget-container {
    margin-bottom: 30px;
}

/* Store grid layout */
.store-list {
    display: grid;
    gap: 20px;
    margin-bottom: 20px;
}

.store-list.columns-1 { grid-template-columns: repeat(1, 1fr); }
.store-list.columns-2 { grid-template-columns: repeat(2, 1fr); }
.store-list.columns-3 { grid-template-columns: repeat(3, 1fr); }
.store-list.columns-4 { grid-template-columns: repeat(4, 1fr); }
.store-list.columns-5 { grid-template-columns: repeat(5, 1fr); }
.store-list.columns-6 { grid-template-columns: repeat(6, 1fr); }

/* Responsive grid adjustments */
@media (max-width: 1024px) {
    .store-list.columns-5, .store-list.columns-6 {
        grid-template-columns: repeat(4, 1fr);
    }
}

@media (max-width: 768px) {
    .store-list.columns-4, .store-list.columns-5, .store-list.columns-6 {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 767px) {
    .store-list.columns-3, .store-list.columns-4, .store-list.columns-5, .store-list.columns-6 {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .store-list {
        grid-template-columns: repeat(1, 1fr) !important;
    }
}

/* Store item */
.store-item {
    padding: 20px;
    border: 1px solid #e5e5e5;
    border-radius: 5px;
    background-color: #fff;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    text-align: center;
    transition: all 0.3s ease;
}

.store-item:hover {
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

/* Store thumbnail with optimized dimensions */
.store-thumbnail {
    margin-bottom: 0;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
}

.store-thumbnail img {
    display: block;
    width: 100%;
    height: auto;
    border-radius: 5px;
    transition: transform 0.3s ease;
    aspect-ratio: 250 / 200;
}

.store-thumbnail:hover img {
    transform: scale(1.05);
}

/* Title wrapper styles */
.store-title-wrapper {
    display: flex;
    align-items: center;
    margin-top: 15px;
    flex-wrap: wrap;
    position: relative;
}

/* Right-aligned badge wrapper */
.store-title-wrapper.badge-right-aligned {
    justify-content: space-between;
}

.store-title-wrapper.badge-right-aligned .store-title {
    margin-right: 10px;
}

/* Coupon count badge styles */
.coupon-count-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    background-color: #4dabf7;
    color: #fff;
    border-radius: 50px;
    font-size: 12px;
    font-weight: bold;
    padding: 3px 8px;
    margin-left: 5px;
    white-space: nowrap;
}

/* Make badge fit content width */
.badge-right-aligned .coupon-count-badge {
    margin-left: auto;
}

/* Store title */
.store-title {
    margin: 10px 0;
    font-size: 18px;
}

.store-title a {
    color: inherit;
    text-decoration: none;
}

.store-title a:hover {
    color: #4dabf7;
}

/* Store network */
.store-network {
    font-size: 14px;
    color: #666;
    margin-bottom: 15px;
}

/* Store actions */
.store-actions {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 15px;
}

.store-actions a {
    display: block;
    text-align: center;
    padding: 10px 15px;
    background-color: #4dabf7;
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.store-actions a:hover {
    background-color: #2b96e0;
}

/* Pagination styles */
.store-pagination {
    margin-top: 30px;
    text-align: center;
}

.store-pagination ul {
    display: inline-flex;
    flex-wrap: wrap;
    justify-content: center;
    list-style: none;
    padding: 0;
    margin: 0;
    background-color: transparent !important;
}

.store-pagination .page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 12px;
    margin: 0 3px;
    background-color: #f8f9fa;
    color: #333;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
    min-width: 36px;
    height: 36px;
    box-sizing: border-box;
}

.store-pagination .page-numbers.current {
    background-color: #4dabf7;
    color: #fff;
}

.store-pagination .page-numbers:hover:not(.current) {
    background-color: #e9ecef;
}

.store-pagination ul.page-numbers {
    background-color: transparent !important;
}

.store-pagination ul li {
    margin: 0 3px;
    display: flex;
    background-color: transparent !important;
}

.store-pagination ul li:hover {
    background-color: transparent !important;
}

/* Fix for icon alignment */
.store-pagination .page-numbers i,
.store-pagination .page-numbers svg {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 1em;
    height: 1em;
}
