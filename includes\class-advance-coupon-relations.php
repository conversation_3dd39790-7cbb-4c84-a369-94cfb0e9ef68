<?php
/**
 * Handle relationships between stores and coupons
 */
class Advance_Coupon_Relations {

    /**
     * Constructor
     */
    public function __construct() {
        // Hook into store deletion
        add_action('before_delete_post', array($this, 'handle_store_deletion'), 10, 1);

        // Hook into trash/untrash actions
        add_action('wp_trash_post', array($this, 'handle_store_trash'), 10, 1);
        add_action('untrash_post', array($this, 'handle_store_untrash'), 10, 1);

        // Hook into store category changes
        add_action('set_object_terms', array($this, 'handle_store_category_change'), 10, 6);
    }

    /**
     * Handle store deletion - delete related coupons
     */
    public function handle_store_deletion($post_id) {
        // Check if the post being deleted is a store
        if (get_post_type($post_id) !== 'store') {
            return;
        }

        // Get all coupons related to this store (including those in trash)
        $related_coupons = $this->get_coupons_by_store($post_id);

        // Delete each related coupon
        foreach ($related_coupons as $coupon) {
            wp_delete_post($coupon->ID, true); // Force delete (skip trash)
        }

        // Also get and delete all trashed coupons related to this store
        $trashed_coupons = $this->get_coupons_by_store($post_id, 'trash');

        // Delete each trashed coupon
        foreach ($trashed_coupons as $coupon) {
            wp_delete_post($coupon->ID, true); // Force delete
        }
    }

    /**
     * Handle store being trashed - trash related coupons
     */
    public function handle_store_trash($post_id) {
        // Check if the post being trashed is a store
        if (get_post_type($post_id) !== 'store') {
            return;
        }

        // Get all coupons related to this store
        $related_coupons = $this->get_coupons_by_store($post_id);

        // Trash each related coupon
        foreach ($related_coupons as $coupon) {
            wp_trash_post($coupon->ID);
        }
    }

    /**
     * Handle store being untrashed - untrash related coupons
     */
    public function handle_store_untrash($post_id) {
        // Check if the post being untrashed is a store
        if (get_post_type($post_id) !== 'store') {
            return;
        }

        // Get all trashed coupons related to this store
        $related_coupons = $this->get_coupons_by_store($post_id, 'trash');

        // Untrash each related coupon
        foreach ($related_coupons as $coupon) {
            wp_untrash_post($coupon->ID);
        }
    }

    /**
     * Get all coupons related to a specific store
     */
    private function get_coupons_by_store($store_id, $post_status = 'any') {
        return get_posts(array(
            'post_type' => 'coupon',
            'post_status' => $post_status,
            'posts_per_page' => -1,
            'meta_query' => array(
                array(
                    'key' => '_store_id',
                    'value' => $store_id,
                    'compare' => '=',
                ),
            ),
        ));
    }

    /**
     * Handle store category changes - update related coupons
     *
     * @param int    $object_id  Object ID.
     * @param array  $terms      An array of object terms.
     * @param array  $tt_ids     An array of term taxonomy IDs.
     * @param string $taxonomy   Taxonomy slug.
     * @param bool   $append     Whether to append new terms to the old terms.
     * @param array  $old_tt_ids Old array of term taxonomy IDs.
     */
    public function handle_store_category_change($object_id, $terms, $tt_ids, $taxonomy, $append, $old_tt_ids) {
        // Only proceed if this is a store category change
        if ($taxonomy !== 'store_category' || get_post_type($object_id) !== 'store') {
            return;
        }

        // Get all coupons related to this store
        $coupons = $this->get_coupons_by_store($object_id);

        if (empty($coupons)) {
            return;
        }

        // Get the store categories
        $store_terms = wp_get_object_terms($object_id, 'store_category', array('fields' => 'ids'));

        // Update each coupon with the new store categories
        foreach ($coupons as $coupon) {
            update_post_meta($coupon->ID, '_store_categories', $store_terms);
        }
    }
}

// Initialize the class
new Advance_Coupon_Relations();
