# Button Styling Guide

This document explains how to customize the appearance of different button types in the Advance Coupon plugin.

## CSS Variables

The plugin now uses CSS variables to control the styling of all buttons. You can easily customize these variables to change the appearance of buttons without modifying the core CSS files.

### How to Customize

Add your custom CSS to your theme's stylesheet or use a custom CSS plugin. Override any of the variables below to change the button styling.

```css
:root {
    /* Change any of these variables to customize button appearance */
    
    /* Common button properties */
    --button-padding: 12px 25px;
    --button-font-weight: 600;
    --button-font-size: 14px;
    --button-border-radius: 50px;
    --button-transition: all 0.3s ease;
    --button-letter-spacing: 0.5px;
    
    /* Get Deal button properties */
    --deal-button-bg: #4dabf7;
    --deal-button-color: #fff;
    --deal-button-hover-bg: #339af0;
    --deal-button-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --deal-button-hover-shadow: 0 4px 8px rgba(0,0,0,0.1);
    
    /* Print Code button properties */
    --print-button-bg: transparent;
    --print-button-color: #4dabf7;
    --print-button-border: 1px dashed #4dabf7;
    --print-button-hover-bg: rgba(77, 171, 247, 0.1);
    --print-button-hover-shadow: 0 4px 8px rgba(0,0,0,0.05);
    
    /* Get Code button properties */
    --code-button-bg: #5B9EFF;
    --code-button-color: #fff;
    --code-button-hover-bg: #4a8cff;
    --code-button-shadow: 0 2px 4px rgba(0,0,0,0.1);
    
    /* Code preview properties */
    --code-preview-width: 80%;
    --code-preview-border: 2px dashed #5B9EFF;
    --code-preview-color: #5B9EFF;
    --code-preview-bg: transparent;
    --code-preview-font-family: monospace;
    --code-preview-font-weight: bold;
    --code-preview-font-size: 14px;
    --code-preview-padding-right: 20px;
}
```

## Examples

### Changing Button Colors

To change the color of the "Get Deal" button:

```css
:root {
    --deal-button-bg: #ff5722; /* Orange background */
    --deal-button-hover-bg: #e64a19; /* Darker orange on hover */
}
```

### Changing Button Shape

To make all buttons more square:

```css
:root {
    --button-border-radius: 8px; /* Less rounded corners */
}
```

### Changing Code Preview Style

To change the code preview appearance:

```css
:root {
    --code-preview-border: 2px solid #5B9EFF; /* Solid border instead of dashed */
    --code-preview-bg: #f8f9fa; /* Light background */
    --code-preview-width: 70%; /* Narrower width */
}
```

## Button Types

### Get Deal Button

The "Get Deal" button is used for Online Sale coupons. It has a solid background color with an arrow icon.

### Print Code Button

The "Print Code" button is used for In-store Code coupons. It has a transparent background with a dashed border.

### Get Code Button

The "Get Code" button is used for Online Code coupons. It has a unique layered design with:
1. A main button with solid background
2. A ghost element that shows the last 2 characters of the code

## Advanced Customization

For more advanced customization, you can also target specific CSS classes directly:

- `.deal-button` - Targets the "Get Deal" button
- `.print-button` - Targets the "Print Code" button
- `.code-button` - Targets the main part of the "Get Code" button
- `.code-preview` - Targets the code preview part of the "Get Code" button
- `.code-button-container` - Targets the container of the "Get Code" button
