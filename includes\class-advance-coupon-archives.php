<?php
/**
 * Archive handling for Advance Coupon
 */
class Advance_Coupon_Archives {

    /**
     * Constructor
     */
    public function __construct() {
        // Pre get posts filter to handle custom archives
        add_action('pre_get_posts', array($this, 'pre_get_posts'));

        // Add body class for store category archives
        add_filter('body_class', array($this, 'add_body_classes'));
    }

    /**
     * Modify the main query for custom archives
     */
    public function pre_get_posts($query) {
        // Only modify the main query on the frontend
        if (!is_admin() && $query->is_main_query()) {
            // Check if we're on a store category archive
            if (is_tax('store_category')) {
                // Set up the query to get stores in this category
                $query->set('post_type', 'store');
                $query->set('posts_per_page', 12); // Adjust as needed
            }

            // Check if we're on the store post type archive
            if (is_post_type_archive('store')) {
                $query->set('posts_per_page', 12); // Adjust as needed
            }
        }

        return $query;
    }

    /**
     * Add custom body classes for store archives
     */
    public function add_body_classes($classes) {
        if (is_tax('store_category')) {
            $classes[] = 'store-category-archive';
            $term = get_queried_object();
            if ($term) {
                $classes[] = 'store-category-' . $term->slug;
            }
        }

        if (is_post_type_archive('store')) {
            $classes[] = 'store-archive';
        }

        return $classes;
    }


}

// Initialize the archives handler
new Advance_Coupon_Archives();
