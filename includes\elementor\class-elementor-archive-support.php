<?php
/**
 * Elementor Archive Support for Advance Coupon
 */
class Elementor_Archive_Support {

    /**
     * Constructor
     */
    public function __construct() {
        // Make sure store_category is available in Elementor conditions
        add_filter('elementor/theme/get_public_post_types', array($this, 'add_store_post_type'));

        // Register store_category taxonomy for Elementor
        add_filter('elementor/theme/get_public_taxonomies', array($this, 'add_store_category_taxonomy'));

        // Add custom template types
        add_action('elementor/theme/register_locations', array($this, 'register_elementor_locations'));

        // Force Elementor to use our templates
        add_action('template_redirect', array($this, 'force_elementor_templates'), 1);
    }

    /**
     * Add store post type to Elementor public post types
     */
    public function add_store_post_type($post_types) {
        $post_types['store'] = get_post_type_object('store');
        return $post_types;
    }

    /**
     * Add store_category taxonomy to Elementor public taxonomies
     */
    public function add_store_category_taxonomy($taxonomies) {
        $taxonomies['store_category'] = get_taxonomy('store_category');
        return $taxonomies;
    }

    /**
     * Register Elementor Theme Builder locations
     */
    public function register_elementor_locations($theme_builder) {
        // We don't need to register custom locations
        // Elementor Pro already has 'single' and 'archive' locations
        // We just need to make sure our post types and taxonomies are available
    }
    /**
     * Force Elementor to use our templates
     */
    public function force_elementor_templates() {
        if (!class_exists('\ElementorPro\Plugin')) {
            return;
        }

        // Make sure Elementor Pro is fully loaded
        if (!did_action('elementor_pro/init')) {
            return;
        }

        // Make sure the theme builder module is available
        if (!\ElementorPro\Plugin::instance()->modules_manager->get_modules('theme-builder')) {
            return;
        }

        // Check if we're on a single store page
        if (is_singular('store')) {
            // This is a hack to force Elementor to use the template
            add_filter('template_include', function($template) {
                if (is_singular('store')) {
                    $documents = \ElementorPro\Modules\ThemeBuilder\Module::instance()->get_conditions_manager()->get_documents_for_location('single');
                    if (!empty($documents)) {
                        return $template;
                    }
                }
                return $template;
            }, 999999);
        }
    }
}

// Initialize the Elementor archive support
if (did_action('elementor/loaded')) {
    new Elementor_Archive_Support();
}
