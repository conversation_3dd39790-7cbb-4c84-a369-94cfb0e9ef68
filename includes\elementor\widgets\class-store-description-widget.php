<?php
namespace AdvanceCoupon\Elementor\Widgets;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Store Description Widget for Elementor
 *
 * This widget is designed to be used in Elementor Theme Builder for single store pages.
 * It displays the store thumbnail, title, description and a button to visit the store website.
 */
class Store_Description_Widget extends \Elementor\Widget_Base {
    /**
     * Get widget name
     */
    public function get_name() {
        return 'store_description_widget';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return __('Store Description', 'advance-coupon');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-post-info'; // Using a post info icon
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['theme-elements', 'general'];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['store', 'description', 'single', 'detail', 'info'];
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'show_thumbnail',
            [
                'label' => __('Show Thumbnail', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_title',
            [
                'label' => __('Show Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_description',
            [
                'label' => __('Show Description', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'description_limit',
            [
                'label' => __('Description Word Limit', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'min' => 0,
                'max' => 1000,
                'step' => 1,
                'default' => 30,
                'description' => __('Set to 0 for no limit', 'advance-coupon'),
                'condition' => [
                    'show_description' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_read_more',
            [
                'label' => __('Show Read More', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'show_description' => 'yes',
                    'description_limit!' => '0',
                ],
            ]
        );

        $this->add_control(
            'read_more_text',
            [
                'label' => __('Read More Text', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Read More...', 'advance-coupon'),
                'condition' => [
                    'show_description' => 'yes',
                    'show_read_more' => 'yes',
                    'description_limit!' => '0',
                ],
            ]
        );

        $this->add_control(
            'show_button',
            [
                'label' => __('Show Button', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'button_text',
            [
                'label' => __('Button Text', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Visit Store', 'advance-coupon'),
                'condition' => [
                    'show_button' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_social_share',
            [
                'label' => __('Show Social Share', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'social_share_title',
            [
                'label' => __('Share Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Share on:', 'advance-coupon'),
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_facebook',
            [
                'label' => __('Facebook', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_instagram',
            [
                'label' => __('Instagram', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_whatsapp',
            [
                'label' => __('WhatsApp', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_tiktok',
            [
                'label' => __('TikTok', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'actions_layout',
            [
                'label' => __('Actions Layout', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => [
                    'button-left' => __('Button Left, Share Right', 'advance-coupon'),
                    'button-right' => __('Button Right, Share Left', 'advance-coupon'),
                    'full-width' => __('Full Width (Each takes 50%)', 'advance-coupon'),
                ],
                'default' => 'button-left',
                'prefix_class' => 'actions-layout-',
                'condition' => [
                    'show_button' => 'yes',
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_responsive_control(
            'actions_direction',
            [
                'label' => __('Actions Direction', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'options' => [
                    'row' => __('Horizontal', 'advance-coupon'),
                    'column' => __('Vertical', 'advance-coupon'),
                ],
                'default' => 'row',
                'selectors' => [
                    '{{WRAPPER}} .store-description-actions-row' => 'flex-direction: {{VALUE}};',
                ],
                'condition' => [
                    'show_button' => 'yes',
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'social_share_alignment',
            [
                'label' => __('Social Share Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'left',
                'selectors' => [
                    '{{WRAPPER}} .store-description-social-share' => 'text-align: {{VALUE}};',
                ],
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        // Thumbnail Style Section
        $this->start_controls_section(
            'thumbnail_style_section',
            [
                'label' => __('Thumbnail', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_thumbnail' => 'yes',
                ],
            ]
        );

        $this->add_responsive_control(
            'thumbnail_width',
            [
                'label' => __('Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', '%'],
                'range' => [
                    'px' => [
                        'min' => 50,
                        'max' => 500,
                        'step' => 10,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                        'step' => 5,
                    ],
                ],
                'default' => [
                    'unit' => '%',
                    'size' => 100,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-description-thumbnail img' => 'width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'thumbnail_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-thumbnail img',
            ]
        );

        $this->add_control(
            'thumbnail_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-thumbnail img' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'thumbnail_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-thumbnail img',
            ]
        );

        $this->end_controls_section();

        // Title Style Section
        $this->start_controls_section(
            'title_style_section',
            [
                'label' => __('Title', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_title' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-description-title' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-title',
            ]
        );

        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Description Style Section
        $this->start_controls_section(
            'description_style_section',
            [
                'label' => __('Description', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_description' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'description_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-description-content' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'description_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-content',
            ]
        );

        $this->add_responsive_control(
            'description_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-content' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Button Style Section
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => __('Button', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_button' => 'yes',
                ],
            ]
        );

        $this->start_controls_tabs('button_style_tabs');

        // Normal state
        $this->start_controls_tab(
            'button_normal_tab',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .store-description-button' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .store-description-button' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'button_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-button',
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '4',
                    'right' => '4',
                    'bottom' => '4',
                    'left' => '4',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );

        $this->add_responsive_control(
            'button_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '10',
                    'right' => '20',
                    'bottom' => '10',
                    'left' => '20',
                    'unit' => 'px',
                    'isLinked' => false,
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state
        $this->start_controls_tab(
            'button_hover_tab',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'button_hover_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .store-description-button:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#339af0',
                'selectors' => [
                    '{{WRAPPER}} .store-description-button:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_hover_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-description-button:hover' => 'border-color: {{VALUE}};',
                ],
                'condition' => [
                    'button_border_border!' => '',
                ],
            ]
        );

        $this->add_control(
            'button_hover_animation',
            [
                'label' => __('Hover Animation', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HOVER_ANIMATION,
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_responsive_control(
            'button_alignment',
            [
                'label' => __('Button Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'left',
                'selectors' => [
                    '{{WRAPPER}} .store-description-button-wrapper' => 'text-align: {{VALUE}};',
                ],
                'separator' => 'before',
            ]
        );

        $this->add_responsive_control(
            'button_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-button-wrapper' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Social Share Style Section
        $this->start_controls_section(
            'social_share_style_section',
            [
                'label' => __('Social Share', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_social_share' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'social_share_title_color',
            [
                'label' => __('Title Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-description-social-share-title' => 'color: {{VALUE}}',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'social_share_title_typography',
                'label' => __('Title Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-description-social-share-title',
            ]
        );

        $this->add_responsive_control(
            'social_share_spacing',
            [
                'label' => __('Icons Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 10,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-description-social-share-icons a' => 'margin-right: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'social_share_icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 20,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-description-social-share-icons i' => 'font-size: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->start_controls_tabs('social_share_colors_tabs');

        // Normal state
        $this->start_controls_tab(
            'social_share_normal_tab',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'facebook_color',
            [
                'label' => __('Facebook Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#3b5998',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-facebook i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_facebook' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'instagram_color',
            [
                'label' => __('Instagram Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#e1306c',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-instagram i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_instagram' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'whatsapp_color',
            [
                'label' => __('WhatsApp Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#25d366',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-whatsapp i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_whatsapp' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'tiktok_color',
            [
                'label' => __('TikTok Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#000000',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-tiktok i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_tiktok' => 'yes',
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state
        $this->start_controls_tab(
            'social_share_hover_tab',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'facebook_hover_color',
            [
                'label' => __('Facebook Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#2d4373',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-facebook:hover i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_facebook' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'instagram_hover_color',
            [
                'label' => __('Instagram Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#c13584',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-instagram:hover i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_instagram' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'whatsapp_hover_color',
            [
                'label' => __('WhatsApp Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#128c7e',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-whatsapp:hover i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_whatsapp' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'tiktok_hover_color',
            [
                'label' => __('TikTok Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#69c9d0',
                'selectors' => [
                    '{{WRAPPER}} .social-icon-tiktok:hover i' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_tiktok' => 'yes',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_responsive_control(
            'social_share_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-description-social-share' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'separator' => 'before',
            ]
        );

        $this->end_controls_section();

        // Layout Style Section
        $this->start_controls_section(
            'layout_style_section',
            [
                'label' => __('Layout', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'content_alignment',
            [
                'label' => __('Content Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'left',
                'selectors' => [
                    '{{WRAPPER}} .store-description-content-wrapper' => 'text-align: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'column_gap',
            [
                'label' => __('Columns Gap', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', '%'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                    'em' => [
                        'min' => 0,
                        'max' => 10,
                        'step' => 0.1,
                    ],
                    '%' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 30,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-description-wrapper' => 'column-gap: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Get the current store ID
     *
     * @return int|null Store ID or null if not found
     */
    private function get_current_store_id() {
        // If we're on a single store page
        if (is_singular('store')) {
            return get_the_ID();
        }

        // For preview in Elementor editor
        if (\Elementor\Plugin::$instance->editor->is_edit_mode()) {
            // Get the most recent store
            $args = [
                'post_type' => 'store',
                'post_status' => 'publish',
                'posts_per_page' => 1,
                'orderby' => 'date',
                'order' => 'DESC',
            ];

            $recent_stores = get_posts($args);

            if (!empty($recent_stores)) {
                return $recent_stores[0]->ID;
            }
        }

        return null;
    }

    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        $store_id = $this->get_current_store_id();

        if (!$store_id) {
            echo '<div class="store-description-error">' . __('No store found.', 'advance-coupon') . '</div>';
            return;
        }

        $store = get_post($store_id);
        $store_url = get_post_meta($store_id, '_store_url', true);
        $store_network = get_post_meta($store_id, '_store_network', true);
        $thumbnail = get_the_post_thumbnail($store_id, 'store-thumbnail');

        // Process description with word limit
        $description = '';
        if ($settings['show_description'] === 'yes' && !empty($store->post_content)) {
            $content = $store->post_content;

            // Apply word limit if set
            if (!empty($settings['description_limit']) && intval($settings['description_limit']) > 0) {
                $words = explode(' ', strip_tags($content));
                $word_limit = intval($settings['description_limit']);

                if (count($words) > $word_limit) {
                    $limited_content = implode(' ', array_slice($words, 0, $word_limit));
                    $full_content = $content;

                    if ($settings['show_read_more'] === 'yes') {
                        // Remove <p> tags from wpautop for better control
                        $formatted_content = wpautop($limited_content);
                        $formatted_content = preg_replace('/<\/p>$/', '', $formatted_content); // Remove closing </p> tag

                        $description = '<div class="store-description-excerpt">' . $formatted_content . ' <a href="#" class="store-description-read-more">' . esc_html($settings['read_more_text']) . '</a></div>';
                        $description .= '<div class="store-description-full-content" style="display: none;">' . wpautop($full_content) . '</div>';
                    } else {
                        $description = wpautop($limited_content);
                    }
                } else {
                    $description = wpautop($content);
                }
            } else {
                $description = wpautop($content);
            }
        }

        // Prepare social share URLs
        $page_url = get_permalink($store_id);
        $page_title = $store->post_title;
        $facebook_url = 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($page_url);
        $whatsapp_url = 'https://api.whatsapp.com/send?text=' . urlencode($page_title . ' ' . $page_url);
        $instagram_url = 'https://www.instagram.com/'; // Instagram doesn't have direct sharing URL
        $tiktok_url = 'https://www.tiktok.com/'; // TikTok doesn't have direct sharing URL

        // Start output
        ?>
        <div class="store-description-wrapper">
            <?php if ($settings['show_thumbnail'] === 'yes' && $thumbnail) : ?>
            <div class="store-description-thumbnail">
                <?php echo $thumbnail; ?>
            </div>
            <?php endif; ?>

            <div class="store-description-content-wrapper">
                <?php if ($settings['show_title'] === 'yes') : ?>
                <h2 class="store-description-title"><?php echo esc_html($store->post_title); ?></h2>
                <?php endif; ?>

                <?php if ($settings['show_description'] === 'yes' && !empty($description)) : ?>
                <div class="store-description-content">
                    <?php echo $description; ?>
                </div>
                <?php endif; ?>

                <div class="store-description-actions-row">
                    <?php if ($settings['show_button'] === 'yes' && !empty($store_url)) : ?>
                    <div class="store-description-button-wrapper">
                        <a href="<?php echo esc_url($store_url); ?>" class="store-description-button elementor-animation-<?php echo esc_attr($settings['button_hover_animation']); ?>" target="_blank">
                            <?php echo esc_html($settings['button_text']); ?>
                        </a>
                    </div>
                    <?php endif; ?>

                    <?php if ($settings['show_social_share'] === 'yes') : ?>
                    <div class="store-description-social-share">
                        <?php if (!empty($settings['social_share_title'])) : ?>
                        <span class="store-description-social-share-title"><?php echo esc_html($settings['social_share_title']); ?></span>
                        <?php endif; ?>

                        <?php if ($settings['show_facebook'] === 'yes') : ?>
                        <a href="<?php echo esc_url($facebook_url); ?>" class="social-icon-facebook" target="_blank" rel="nofollow">
                            <i class="fa fa-facebook-square fa-lg" aria-hidden="true"></i>
                        </a>
                        <?php endif; ?>

                        <?php if ($settings['show_instagram'] === 'yes') : ?>
                        <a href="<?php echo esc_url($instagram_url); ?>" class="social-icon-instagram" target="_blank" rel="nofollow">
                            <i class="fa fa-instagram fa-lg" aria-hidden="true"></i>
                        </a>
                        <?php endif; ?>

                        <?php if ($settings['show_whatsapp'] === 'yes') : ?>
                        <a href="<?php echo esc_url($whatsapp_url); ?>" class="social-icon-whatsapp" target="_blank" rel="nofollow">
                            <i class="fa fa-whatsapp fa-lg" aria-hidden="true"></i>
                        </a>
                        <?php endif; ?>

                        <?php if ($settings['show_tiktok'] === 'yes') : ?>
                        <a href="<?php echo esc_url($tiktok_url); ?>" class="social-icon-tiktok" target="_blank" rel="nofollow">
                            <i class="fa fa-music fa-lg" aria-hidden="true"></i>
                        </a>
                        <?php endif; ?>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <?php if ($settings['show_description'] === 'yes' && $settings['show_read_more'] === 'yes' && !empty($settings['description_limit']) && intval($settings['description_limit']) > 0) : ?>
        <script>
        jQuery(document).ready(function($) {
            $('.store-description-read-more').on('click', function(e) {
                e.preventDefault();
                var $this = $(this);
                var $excerpt = $this.closest('.store-description-excerpt');
                var $fullContent = $excerpt.siblings('.store-description-full-content');

                if ($fullContent.is(':visible')) {
                    $fullContent.hide();
                    $excerpt.show();
                    $this.text('<?php echo esc_js($settings['read_more_text']); ?>');
                } else {
                    $excerpt.hide();
                    $fullContent.show();
                    $fullContent.append(' <a href="#" class="store-description-read-less"><?php echo esc_js(__('Show Less', 'advance-coupon')); ?></a>');

                    $('.store-description-read-less').on('click', function(e) {
                        e.preventDefault();
                        $fullContent.hide();
                        $excerpt.show();
                        $(this).remove();
                    });
                }
            });
        });
        </script>
        <?php endif; ?>
        <?php
    }
}
