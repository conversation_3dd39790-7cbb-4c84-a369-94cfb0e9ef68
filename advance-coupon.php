<?php
/**
 * Plugin Name: Advance Coupon
 * Plugin URI:
 * Description: A WordPress plugin for managing stores and coupons with Elementor compatibility.
 * Version: 1.12.0
 * Author:
 * Author URI:
 * Text Domain: advance-coupon
 * Domain Path: /languages
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

/*  */// Define plugin constants
define('ADVCOUPON_VERSION', '1.12.0');
define('ADVCOUPON_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('ADVCOUPON_PLUGIN_URL', plugin_dir_url(__FILE__));
define('ADVCOUPON_PLUGIN_BASENAME', plugin_basename(__FILE__));

// Include required files
require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon.php';

// Initialize the plugin
function advance_coupon_init() {
    $plugin = new Advance_Coupon();
    $plugin->init();
}
add_action('plugins_loaded', 'advance_coupon_init');

// Check if Elementor is installed and activated
if (!did_action('elementor/loaded')) {
    add_action('admin_notices', function() {
        if (isset($_GET['activate'])) {
            unset($_GET['activate']);
        }

        $message = sprintf(
            esc_html__('Advance Coupon requires %1$s to be installed and activated.', 'advance-coupon'),
            '<strong>' . esc_html__('Elementor', 'advance-coupon') . '</strong>'
        );

        printf('<div class="notice notice-warning is-dismissible"><p>%1$s</p></div>', $message);
    });
}

// Activation hook
register_activation_hook(__FILE__, 'advance_coupon_activate');
function advance_coupon_activate() {
    // Set a flag to flush rewrite rules on next admin page load
    update_option('advance_coupon_flush_rewrite_rules', true);

    // Store the current version
    update_option('advance_coupon_version', ADVCOUPON_VERSION);
}

// Deactivation hook
register_deactivation_hook(__FILE__, 'advance_coupon_deactivate');
function advance_coupon_deactivate() {
    // Clean up options
    delete_option('advance_coupon_flush_rewrite_rules');

    // Flush rewrite rules immediately on deactivation
    flush_rewrite_rules();
}
