<?php
/**
 * Main plugin class
 */
class Advance_Coupon {
    /**
     * Constructor
     */
    public function __construct() {
        // Nothing to do here
    }

    /**
     * Initialize the plugin
     */
    public function init() {
        // Load text domain
        add_action('init', array($this, 'load_textdomain'));

        // Include required files
        $this->includes();

        // Register taxonomies - register before post types
        add_action('init', array($this, 'register_taxonomies'), 5);

        // Register post types - register after taxonomies
        add_action('init', array($this, 'register_post_types'), 10);

        // Register meta boxes
        add_action('add_meta_boxes', array($this, 'register_meta_boxes'));

        // Save meta box data
        add_action('save_post', array($this, 'save_meta_box_data'));

        // Add admin filters
        add_action('restrict_manage_posts', array($this, 'add_admin_filters'));
        add_filter('parse_query', array($this, 'filter_query_by_network'));

        // Add admin notice for flushing rewrite rules
        add_action('admin_notices', array($this, 'show_flush_rewrite_rules_notice'));
        add_action('admin_init', array($this, 'handle_flush_rewrite_rules'));

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));

        // Register custom image sizes
        add_action('after_setup_theme', array($this, 'register_image_sizes'));

        // Add rewrite rules flush on admin init (only when needed)
        add_action('admin_init', array($this, 'maybe_flush_rewrite_rules'));
    }

    /**
     * Load text domain
     */
    public function load_textdomain() {
        load_plugin_textdomain('advance-coupon', false, dirname(ADVCOUPON_PLUGIN_BASENAME) . '/languages');
    }

    /**
     * Include required files
     */
    public function includes() {
        // Include post types
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/post-types/class-store-post-type.php';
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/post-types/class-coupon-post-type.php';

        // Include meta boxes
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/meta-boxes/class-store-meta-box.php';
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/meta-boxes/class-coupon-meta-box.php';

        // Include cache handler
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-cache.php';

        // Include AJAX handler
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-ajax.php';

        // Include relations handler
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-relations.php';

        // Include archives handler
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-archives.php';

        // Include taxonomy URL rewriter
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-taxonomy-rewrite.php';

        // Include category meta fields
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-category-meta.php';

        // Include scripts and styles handler
        require_once ADVCOUPON_PLUGIN_DIR . 'includes/class-advance-coupon-scripts.php';

        // Include Elementor widgets if Elementor is active
        if (did_action('elementor/loaded')) {
            require_once ADVCOUPON_PLUGIN_DIR . 'includes/elementor/class-elementor-widgets.php';
        }
    }

    /**
     * Register post types
     */
    public function register_post_types() {
        // Initialize Store Post Type
        $store_post_type = new Store_Post_Type();
        $store_post_type->register();

        // Initialize Coupon Post Type
        $coupon_post_type = new Coupon_Post_Type();
        $coupon_post_type->register();
    }

    /**
     * Register taxonomies
     */
    public function register_taxonomies() {
        // Register store category taxonomy
        register_taxonomy(
            'store_category',
            'store',
            array(
                'label' => __('Store Categories', 'advance-coupon'),
                'labels' => array(
                    'name' => __('Categories', 'advance-coupon'),
                    'singular_name' => __('Category', 'advance-coupon'),
                    'menu_name' => __('Categories', 'advance-coupon'),
                    'all_items' => __('All Categories', 'advance-coupon'),
                    'edit_item' => __('Edit Category', 'advance-coupon'),
                    'view_item' => __('View Category', 'advance-coupon'),
                    'update_item' => __('Update Category', 'advance-coupon'),
                    'add_new_item' => __('Add New Category', 'advance-coupon'),
                    'new_item_name' => __('New Category Name', 'advance-coupon'),
                    'search_items' => __('Search Categories', 'advance-coupon'),
                    'popular_items' => __('Popular Categories', 'advance-coupon'),
                    'separate_items_with_commas' => __('Separate categories with commas', 'advance-coupon'),
                    'add_or_remove_items' => __('Add or remove categories', 'advance-coupon'),
                    'choose_from_most_used' => __('Choose from the most used categories', 'advance-coupon'),
                    'not_found' => __('No categories found', 'advance-coupon'),
                ),
                'hierarchical' => true,
                'public' => true,
                'show_ui' => true,
                'show_in_menu' => true,
                'show_in_nav_menus' => true,
                'show_admin_column' => true,
                'show_in_rest' => true,
                'query_var' => true,
                'rewrite' => array(
                    'slug' => 'store-category',
                    'with_front' => true,
                    'hierarchical' => true,
                    'ep_mask' => EP_ALL
                ),
                'capabilities' => array(
                    'manage_terms' => 'manage_categories',
                    'edit_terms' => 'manage_categories',
                    'delete_terms' => 'manage_categories',
                    'assign_terms' => 'edit_posts',
                ),
            )
        );
    }

    /**
     * Register meta boxes
     */
    public function register_meta_boxes() {
        $store_meta_box = new Store_Meta_Box();
        $store_meta_box->register();

        $coupon_meta_box = new Coupon_Meta_Box();
        $coupon_meta_box->register();
    }

    /**
     * Save meta box data
     */
    public function save_meta_box_data($post_id) {
        // Check if we're saving a store post
        if (isset($_POST['post_type']) && 'store' === $_POST['post_type']) {
            $store_meta_box = new Store_Meta_Box();
            $store_meta_box->save($post_id);
        }

        // Check if we're saving a coupon post
        if (isset($_POST['post_type']) && 'coupon' === $_POST['post_type']) {
            $coupon_meta_box = new Coupon_Meta_Box();
            $coupon_meta_box->save($post_id);
        }
    }

    /**
     * Register custom image sizes
     */
    public function register_image_sizes() {
        // Register a custom image size for store thumbnails
        add_image_size('store-thumbnail', 250, 200, false); // 250x200 size, soft crop (maintain aspect ratio)

        // Register a custom image size for printable coupons
        add_image_size('coupon-printable', 600, 450, false); // 4:3 aspect ratio, soft crop
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue store widget styles
        wp_enqueue_style(
            'advance-coupon-store-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue coupon widget styles
        wp_enqueue_style(
            'advance-coupon-coupon-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/coupon-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue store description widget styles
        wp_enqueue_style(
            'advance-coupon-store-description-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-description-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue AJAX pagination styles
        wp_enqueue_style(
            'advance-coupon-store-widget-ajax',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-widget-ajax.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue store coupons widget styles
        wp_enqueue_style(
            'advance-coupon-store-coupons-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-coupons-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue store FAQ widget styles
        wp_enqueue_style(
            'advance-coupon-store-faq-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-faq-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue related stores widget styles
        wp_enqueue_style(
            'advance-coupon-related-stores-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/css/related-stores-widget.css',
            array(),
            ADVCOUPON_VERSION
        );

        // Enqueue Font Awesome 5 for icons
        wp_enqueue_style(
            'font-awesome-5',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css',
            array(),
            '5.15.4'
        );

        // Enqueue Font Awesome 4 for backward compatibility
        wp_enqueue_style(
            'font-awesome-4',
            'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css',
            array(),
            '4.7.0'
        );

        // Enqueue store coupons widget script
        wp_enqueue_script(
            'advance-coupon-store-coupons-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/js/store-coupons-final.js',
            array('jquery'),
            ADVCOUPON_VERSION,
            true
        );

        // Enqueue store FAQ widget script
        wp_enqueue_script(
            'advance-coupon-store-faq-widget',
            ADVCOUPON_PLUGIN_URL . 'assets/js/store-faq-widget.js',
            array('jquery'),
            ADVCOUPON_VERSION,
            true
        );

        // Localize script with AJAX URL and nonce
        wp_localize_script(
            'advance-coupon-store-coupons-widget',
            'advance_coupon_ajax',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('advance_coupon_ajax_nonce')
            )
        );
    }

    /**
     * Add admin filters
     */
    public function add_admin_filters($post_type) {
        // Add network filter for stores
        if ('store' !== $post_type) {
            return;
        }

        // Get all networks
        $networks = $this->get_all_networks();

        // Display the network filter dropdown
        echo '<select name="store_network">';
        echo '<option value="">' . __('All Networks', 'advance-coupon') . '</option>';

        foreach ($networks as $network) {
            $selected = isset($_GET['store_network']) && $_GET['store_network'] === $network ? 'selected="selected"' : '';
            echo '<option value="' . esc_attr($network) . '" ' . $selected . '>' . esc_html($network) . '</option>';
        }

        echo '</select>';

        // Get current category filter value
        $current_category = isset($_GET['store_category']) ? $_GET['store_category'] : '';

        // Get all store categories
        $categories = get_terms(array(
            'taxonomy' => 'store_category',
            'hide_empty' => false,
        ));

        if (!empty($categories) && !is_wp_error($categories)) {
            // Display the category filter dropdown
            echo '<select name="store_category">';
            echo '<option value="">' . __('All Categories', 'advance-coupon') . '</option>';

            foreach ($categories as $category) {
                $selected = $current_category === $category->slug ? 'selected="selected"' : '';
                echo '<option value="' . esc_attr($category->slug) . '" ' . $selected . '>' . esc_html($category->name) . '</option>';
            }

            echo '</select>';
        }
    }

    /**
     * Filter query by network and category
     */
    public function filter_query_by_network($query) {
        global $pagenow;

        // Check if we're in the admin area, on the post listing page
        if (!is_admin() || 'edit.php' !== $pagenow) {
            return;
        }

        // Make sure we're on the store post type
        if (!isset($_GET['post_type']) || 'store' !== $_GET['post_type']) {
            return;
        }

        // Initialize meta_query if needed
        if (!isset($query->query_vars['meta_query'])) {
            $query->query_vars['meta_query'] = array();
        }

        // Filter by network if set
        if (isset($_GET['store_network']) && !empty($_GET['store_network'])) {
            $query->query_vars['meta_query'][] = array(
                'key' => '_store_network',
                'value' => sanitize_text_field($_GET['store_network']),
                'compare' => '=',
            );
        }

        // Filter by category if set
        if (isset($_GET['store_category']) && !empty($_GET['store_category'])) {
            $query->set('tax_query', array(
                array(
                    'taxonomy' => 'store_category',
                    'field'    => 'slug',
                    'terms'    => sanitize_text_field($_GET['store_category']),
                ),
            ));
        }
    }

    /**
     * Get all networks
     */
    private function get_all_networks() {
        global $wpdb;

        // Get all unique network values from store meta
        $networks = $wpdb->get_col(
            "SELECT DISTINCT meta_value FROM {$wpdb->postmeta}
            WHERE meta_key = '_store_network'
            AND meta_value != ''
            ORDER BY meta_value ASC"
        );

        return $networks;
    }

    /**
     * Maybe flush rewrite rules if needed
     * This function checks if we need to flush rewrite rules and does it only when necessary
     */
    public function maybe_flush_rewrite_rules() {
        // Get the current version from the database
        $current_version = get_option('advance_coupon_version', '0');

        // If the version has changed or the flush flag is set, flush rewrite rules
        if (version_compare($current_version, ADVCOUPON_VERSION, '!=') || get_option('advance_coupon_flush_rewrite_rules', false)) {
            flush_rewrite_rules();

            // Update the version in the database
            update_option('advance_coupon_version', ADVCOUPON_VERSION);

            // Delete the flush flag
            delete_option('advance_coupon_flush_rewrite_rules');
        }
    }

    /**
     * Show admin notice for flushing rewrite rules
     */
    public function show_flush_rewrite_rules_notice() {
        // Only show to admins
        if (!current_user_can('manage_options')) {
            return;
        }

        // Check if we're on the store or taxonomy pages
        $screen = get_current_screen();
        if (!$screen || !in_array($screen->id, array('edit-store', 'edit-store_category'))) {
            return;
        }

        // Show the notice
        echo '<div class="notice notice-info is-dismissible">';
        echo '<p>' . __('If you are experiencing issues with store category pages, you may need to flush the rewrite rules.', 'advance-coupon') . ' ';
        echo '<a href="' . wp_nonce_url(add_query_arg('advance_coupon_flush_rewrite_rules', 'true'), 'advance_coupon_flush_rewrite_rules', 'advance_coupon_nonce') . '" class="button button-primary">' . __('Flush Rewrite Rules', 'advance-coupon') . '</a></p>';
        echo '</div>';
    }

    /**
     * Handle flush rewrite rules action
     */
    public function handle_flush_rewrite_rules() {
        if (isset($_GET['advance_coupon_flush_rewrite_rules']) && $_GET['advance_coupon_flush_rewrite_rules'] === 'true') {
            // Check nonce
            if (!isset($_GET['advance_coupon_nonce']) || !wp_verify_nonce($_GET['advance_coupon_nonce'], 'advance_coupon_flush_rewrite_rules')) {
                wp_die(__('Security check failed', 'advance-coupon'));
            }

            // Check permissions
            if (!current_user_can('manage_options')) {
                wp_die(__('You do not have permission to perform this action', 'advance-coupon'));
            }

            // Flush rewrite rules
            flush_rewrite_rules();

            // Redirect back
            wp_redirect(remove_query_arg(array('advance_coupon_flush_rewrite_rules', 'advance_coupon_nonce')));
            exit;
        }
    }

}
