<?php
/**
 * Cache handling for Advance Coupon
 */
class Advance_Coupon_Cache {
    
    /**
     * Constructor
     */
    public function __construct() {
        // Clear store count cache when stores are added/updated/deleted
        add_action('save_post', array($this, 'clear_store_count_cache'), 10, 3);
        add_action('before_delete_post', array($this, 'clear_store_count_cache'), 10, 3);
        add_action('trash_post', array($this, 'clear_store_count_cache'), 10, 3);
        add_action('untrash_post', array($this, 'clear_store_count_cache'), 10, 3);
    }
    
    /**
     * Clear store count cache when stores are modified
     */
    public function clear_store_count_cache($post_id, $post = null, $update = null) {
        // If post object is not provided (like in before_delete_post), get it
        if (null === $post) {
            $post = get_post($post_id);
        }
        
        // Only clear cache for store post type
        if ($post && 'store' === $post->post_type) {
            delete_transient('advance_coupon_store_count');
        }
    }
}

// Initialize the cache handler
new Advance_Coupon_Cache();
