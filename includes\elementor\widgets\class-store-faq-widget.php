<?php
namespace AdvanceCoupon\Elementor\Widgets;

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Store FAQ Widget for Elementor
 *
 * This widget displays FAQs for a specific store in an accordion style.
 */
class Store_FAQ_Widget extends \Elementor\Widget_Base {
    /**
     * Get widget name
     */
    public function get_name() {
        return 'store_faq_widget';
    }

    /**
     * Get widget title
     */
    public function get_title() {
        return __('Store FAQs', 'advance-coupon');
    }

    /**
     * Get widget icon
     */
    public function get_icon() {
        return 'eicon-accordion';
    }

    /**
     * Get widget categories
     */
    public function get_categories() {
        return ['theme-elements', 'general'];
    }

    /**
     * Get widget keywords
     */
    public function get_keywords() {
        return ['store', 'faq', 'accordion', 'question', 'answer'];
    }

    /**
     * Get stores options for dropdown
     */
    private function get_stores_options() {
        $options = [
            'current' => __('Current Store', 'advance-coupon'),
        ];

        $stores = get_posts([
            'post_type' => 'store',
            'post_status' => 'publish',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
        ]);

        foreach ($stores as $store) {
            $options[$store->ID] = $store->post_title;
        }

        return $options;
    }

    /**
     * Get current store ID
     */
    private function get_current_store_id() {
        $settings = $this->get_settings_for_display();

        if ($settings['store_id'] === 'current') {
            // Try to get store ID from the current page
            global $post;

            if (is_singular('store') && $post) {
                return $post->ID;
            }

            // Check if we're on a page that has a store ID in the query string
            if (isset($_GET['store_id'])) {
                return intval($_GET['store_id']);
            }

            return 0;
        }

        return $settings['store_id'];
    }

    /**
     * Register widget controls
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'store_id',
            [
                'label' => __('Store', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'options' => $this->get_stores_options(),
                'default' => 'current',
                'label_block' => true,
            ]
        );

        $this->add_control(
            'title',
            [
                'label' => __('Widget Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::TEXT,
                'default' => __('Frequently Asked Questions', 'advance-coupon'),
                'label_block' => true,
            ]
        );

        $this->add_control(
            'show_title',
            [
                'label' => __('Show Widget Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Show', 'advance-coupon'),
                'label_off' => __('Hide', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'open_first_item',
            [
                'label' => __('Open First Item', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'allow_multiple_open',
            [
                'label' => __('Allow Multiple Open Items', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
            ]
        );

        $this->add_control(
            'icon_position',
            [
                'label' => __('Icon Position', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'right',
                'options' => [
                    'left' => __('Left', 'advance-coupon'),
                    'right' => __('Right', 'advance-coupon'),
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Widget
        $this->start_controls_section(
            'widget_style_section',
            [
                'label' => __('Widget', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'widget_title_color',
            [
                'label' => __('Widget Title Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-widget-title' => 'color: {{VALUE}};',
                ],
                'condition' => [
                    'show_title' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'widget_title_typography',
                'selector' => '{{WRAPPER}} .store-faq-widget-title',
                'condition' => [
                    'show_title' => 'yes',
                ],
            ]
        );

        $this->add_responsive_control(
            'widget_title_margin',
            [
                'label' => __('Widget Title Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-widget-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'show_title' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'widget_background_color',
            [
                'label' => __('Widget Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-widget' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'widget_border',
                'selector' => '{{WRAPPER}} .store-faq-widget',
            ]
        );

        $this->add_responsive_control(
            'widget_border_radius',
            [
                'label' => __('Widget Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-widget' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'widget_padding',
            [
                'label' => __('Widget Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-widget' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'widget_box_shadow',
                'selector' => '{{WRAPPER}} .store-faq-widget',
            ]
        );

        $this->end_controls_section();

        // Style Section - Question
        $this->start_controls_section(
            'question_style_section',
            [
                'label' => __('Question', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'question_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'question_active_background_color',
            [
                'label' => __('Active Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header.active' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'question_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-question' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'question_active_color',
            [
                'label' => __('Active Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header.active .store-faq-item-question' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'question_typography',
                'selector' => '{{WRAPPER}} .store-faq-item-question',
            ]
        );

        $this->add_responsive_control(
            'question_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'question_border',
                'selector' => '{{WRAPPER}} .store-faq-item-header',
            ]
        );

        $this->add_responsive_control(
            'question_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'question_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Answer
        $this->start_controls_section(
            'answer_style_section',
            [
                'label' => __('Answer', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'answer_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-content' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'answer_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-answer' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'answer_typography',
                'selector' => '{{WRAPPER}} .store-faq-item-answer',
            ]
        );

        $this->add_responsive_control(
            'answer_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-content' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'answer_border',
                'selector' => '{{WRAPPER}} .store-faq-item-content',
            ]
        );

        $this->add_responsive_control(
            'answer_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-content' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'answer_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-content' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Icon
        $this->start_controls_section(
            'icon_style_section',
            [
                'label' => __('Icon', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'icon_expanded',
            [
                'label' => __('Expanded Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-minus',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'icon_collapsed',
            [
                'label' => __('Collapsed Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-plus',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'icon_color',
            [
                'label' => __('Icon Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-icon i' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .store-faq-item-icon svg' => 'fill: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'icon_active_color',
            [
                'label' => __('Active Icon Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-header.active .store-faq-item-icon i' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .store-faq-item-header.active .store-faq-item-icon svg' => 'fill: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                    'em' => [
                        'min' => 0,
                        'max' => 10,
                    ],
                    'rem' => [
                        'min' => 0,
                        'max' => 10,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item-icon i' => 'font-size: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .store-faq-item-icon svg' => 'width: {{SIZE}}{{UNIT}}; height: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'icon_spacing',
            [
                'label' => __('Icon Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                    ],
                    'em' => [
                        'min' => 0,
                        'max' => 5,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}}' => '--faq-icon-spacing: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Item
        $this->start_controls_section(
            'item_style_section',
            [
                'label' => __('FAQ Item', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'item_spacing',
            [
                'label' => __('Items Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                    'em' => [
                        'min' => 0,
                        'max' => 10,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item:not(:last-child)' => 'margin-bottom: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'item_border',
                'selector' => '{{WRAPPER}} .store-faq-item',
            ]
        );

        $this->add_responsive_control(
            'item_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-faq-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'item_box_shadow',
                'selector' => '{{WRAPPER}} .store-faq-item',
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        $store_id = $this->get_current_store_id();

        if (!$store_id) {
            echo '<div class="store-faq-error">' . __('No store found.', 'advance-coupon') . '</div>';
            return;
        }

        // Get store FAQs
        $faqs = get_post_meta($store_id, '_store_faqs', true);

        if (empty($faqs)) {
            echo '<div class="store-faq-error">' . __('No FAQs found for this store.', 'advance-coupon') . '</div>';
            return;
        }

        $widget_id = $this->get_id();
        $icon_position_class = 'icon-position-' . $settings['icon_position'];
        // Convert boolean settings to string 'true'/'false' for data attributes
        $allow_multiple = $settings['allow_multiple_open'] === 'yes' ? 'true' : 'false';

        // Start output
        ?>
        <div class="store-faq-widget" id="store-faq-widget-<?php echo esc_attr($widget_id); ?>" data-allow-multiple="<?php echo esc_attr($allow_multiple); ?>" data-open-first="<?php echo esc_attr($settings['open_first_item']); ?>">
            <?php if ($settings['show_title'] === 'yes' && !empty($settings['title'])) : ?>
                <h3 class="store-faq-widget-title"><?php echo esc_html($settings['title']); ?></h3>
            <?php endif; ?>

            <div class="store-faq-items">
                <?php foreach ($faqs as $index => $faq) :
                    $is_active = ($index === 0 && $settings['open_first_item'] === 'yes') ? 'active' : '';
                    $is_hidden = ($index === 0 && $settings['open_first_item'] === 'yes') ? '' : 'style="display: none;"';

                    // Debug output to check settings
                    // echo '<!-- Open First Item: ' . $settings['open_first_item'] . ', Index: ' . $index . ', Active: ' . $is_active . ' -->';
                ?>
                    <div class="store-faq-item">
                        <div class="store-faq-item-header <?php echo esc_attr($icon_position_class); ?> <?php echo esc_attr($is_active); ?>">
                            <?php if ($settings['icon_position'] === 'left') : ?>
                                <div class="store-faq-item-icon">
                                    <span class="icon-expanded" <?php echo $is_active ? '' : 'style="display: none;"'; ?>>
                                        <?php \Elementor\Icons_Manager::render_icon($settings['icon_expanded'], ['aria-hidden' => 'true']); ?>
                                    </span>
                                    <span class="icon-collapsed" <?php echo $is_active ? 'style="display: none;"' : ''; ?>>
                                        <?php \Elementor\Icons_Manager::render_icon($settings['icon_collapsed'], ['aria-hidden' => 'true']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <div class="store-faq-item-question"><?php echo esc_html($faq['question']); ?></div>

                            <?php if ($settings['icon_position'] === 'right') : ?>
                                <div class="store-faq-item-icon">
                                    <span class="icon-expanded" <?php echo $is_active ? '' : 'style="display: none;"'; ?>>
                                        <?php \Elementor\Icons_Manager::render_icon($settings['icon_expanded'], ['aria-hidden' => 'true']); ?>
                                    </span>
                                    <span class="icon-collapsed" <?php echo $is_active ? 'style="display: none;"' : ''; ?>>
                                        <?php \Elementor\Icons_Manager::render_icon($settings['icon_collapsed'], ['aria-hidden' => 'true']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="store-faq-item-content" <?php echo $is_hidden; ?>>
                            <div class="store-faq-item-answer"><?php echo wp_kses_post($faq['answer']); ?></div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        <?php
    }
}
