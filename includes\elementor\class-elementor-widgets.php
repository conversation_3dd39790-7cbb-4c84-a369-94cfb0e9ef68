<?php
if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

// Include Elementor template override - this is the critical fix
require_once(__DIR__ . '/class-elementor-template-override.php');

// Include Elementor archive support
require_once(__DIR__ . '/class-elementor-archive-support.php');

/**
 * Register Elementor Widgets
 */
function register_advance_coupon_widgets($widgets_manager = null) {
    // Include widget files
    require_once(__DIR__ . '/widgets/class-coupon-widget.php');
    require_once(__DIR__ . '/widgets/class-store-widget.php');
    require_once(__DIR__ . '/widgets/class-store-description-widget.php');
    require_once(__DIR__ . '/widgets/class-store-coupons-widget.php');
    require_once(__DIR__ . '/widgets/class-store-faq-widget.php');
    require_once(__DIR__ . '/widgets/class-related-stores-widget.php');
    require_once(__DIR__ . '/widgets/class-store-categories-widget.php');
    require_once(__DIR__ . '/widgets/class-category-coupons-widget.php');
    require_once(__DIR__ . '/widgets/class-store-carousel-widget.php');

    // Register widgets
    if (version_compare(ELEMENTOR_VERSION, '3.5.0', '>=')) {
        // Elementor 3.5.0 or greater
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Coupon_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Store_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Store_Description_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Store_Coupons_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Store_FAQ_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Related_Stores_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Category_Coupons_Widget());
        $widgets_manager->register(new \Store_Categories_Widget());
        $widgets_manager->register(new \AdvanceCoupon\Elementor\Widgets\Store_Carousel_Widget());
    } else {
        // Older versions of Elementor
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Coupon_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Store_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Store_Description_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Store_Coupons_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Store_FAQ_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Related_Stores_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Category_Coupons_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \Store_Categories_Widget());
        \Elementor\Plugin::instance()->widgets_manager->register_widget_type(new \AdvanceCoupon\Elementor\Widgets\Store_Carousel_Widget());
    }
}

// Register widgets for Elementor 3.5.0+
add_action('elementor/widgets/register', 'register_advance_coupon_widgets');

// Register widgets for older Elementor versions
add_action('elementor/widgets/widgets_registered', 'register_advance_coupon_widgets');
