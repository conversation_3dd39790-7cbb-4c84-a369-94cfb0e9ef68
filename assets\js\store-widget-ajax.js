/**
 * AJAX-based pagination for Store Widget
 */
(function($) {
    'use strict';
    
    // Initialize AJAX pagination
    function initAjaxPagination() {
        $('.store-widget-container').each(function() {
            var $container = $(this);
            var widgetId = $container.data('widget-id');
            
            // Handle pagination clicks
            $container.on('click', '.store-pagination a.page-numbers', function(e) {
                e.preventDefault();
                
                var $link = $(this);
                var pageUrl = $link.attr('href');
                var page = 1;
                
                // Extract page number from URL
                var matches = pageUrl.match(/store-page=(\d+)/);
                if (matches) {
                    page = parseInt(matches[1]);
                }
                
                // Show loading indicator
                $container.addClass('loading');
                
                // Make AJAX request
                $.ajax({
                    url: advance_coupon_ajax.ajax_url,
                    type: 'POST',
                    data: {
                        action: 'load_store_widget_page',
                        widget_id: widgetId,
                        page: page,
                        settings: $container.data('settings'),
                        nonce: advance_coupon_ajax.nonce
                    },
                    success: function(response) {
                        if (response.success) {
                            // Update container with new content
                            $container.html(response.data.html);
                            
                            // Scroll to top of widget
                            $('html, body').animate({
                                scrollTop: $container.offset().top - 50
                            }, 500);
                        } else {
                            console.error('Error loading stores:', response.data.message);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('AJAX error:', error);
                    },
                    complete: function() {
                        // Remove loading indicator
                        $container.removeClass('loading');
                    }
                });
            });
        });
    }
    
    // Initialize when document is ready
    $(document).ready(function() {
        initAjaxPagination();
        
        // Also initialize when Elementor frontend is initialized (for Elementor editor)
        $(document).on('elementor/frontend/init', function() {
            if (typeof elementorFrontend !== 'undefined') {
                elementorFrontend.hooks.addAction('frontend/element_ready/store-widget.default', function() {
                    initAjaxPagination();
                });
            }
        });
    });
    
})(jQuery);
