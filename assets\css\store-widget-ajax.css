/**
 * Styles for AJAX-based Store Widget
 */

/* Loading indicator */
.store-widget-container.loading {
    position: relative;
    min-height: 200px;
}

.store-widget-container.loading:after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.7);
    z-index: 10;
}

.store-widget-container.loading:before {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    margin: -20px 0 0 -20px;
    border-radius: 50%;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #4dabf7;
    animation: spin 1s linear infinite;
    z-index: 11;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
