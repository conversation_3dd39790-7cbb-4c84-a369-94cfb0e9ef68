<?php
/**
 * Coupon Post Type
 */
class Coupon_Post_Type {
    /**
     * Constructor
     */
    public function __construct() {
        // Register custom columns for the admin list table
        add_filter('manage_coupon_posts_columns', array($this, 'set_custom_columns'));
        add_action('manage_coupon_posts_custom_column', array($this, 'render_custom_columns'), 10, 2);

        // Make columns sortable
        add_filter('manage_edit-coupon_sortable_columns', array($this, 'sortable_columns'));

        // Add admin filters
        add_action('restrict_manage_posts', array($this, 'add_admin_filters'));
        add_filter('parse_query', array($this, 'filter_coupons_by_filters'));
    }

    /**
     * Register the post type
     */
    public function register() {
        $labels = array(
            'name'                  => _x('Coupons', 'Post type general name', 'advance-coupon'),
            'singular_name'         => _x('Coupon', 'Post type singular name', 'advance-coupon'),
            'menu_name'             => _x('Coupons', 'Admin Menu text', 'advance-coupon'),
            'name_admin_bar'        => _x('Coupon', 'Add New on Toolbar', 'advance-coupon'),
            'add_new'               => __('Add New', 'advance-coupon'),
            'add_new_item'          => __('Add New Coupon', 'advance-coupon'),
            'new_item'              => __('New Coupon', 'advance-coupon'),
            'edit_item'             => __('Edit Coupon', 'advance-coupon'),
            'view_item'             => __('View Coupon', 'advance-coupon'),
            'all_items'             => __('All Coupons', 'advance-coupon'),
            'search_items'          => __('Search Coupons', 'advance-coupon'),
            'parent_item_colon'     => __('Parent Coupons:', 'advance-coupon'),
            'not_found'             => __('No coupons found.', 'advance-coupon'),
            'not_found_in_trash'    => __('No coupons found in Trash.', 'advance-coupon'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => false, // Set to false to disable single view
            'publicly_queryable' => false, // Set to false to disable single view
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => false, // Set to false to disable single view
            'rewrite'            => false, // Set to false to disable single view
            'capability_type'    => 'post',
            'has_archive'        => false, // Set to false to disable archive view
            'hierarchical'       => false,
            'menu_position'      => null,
            'menu_icon'          => 'dashicons-tickets-alt',
            'supports'           => array('title'),
        );

        register_post_type('coupon', $args);
    }

    /**
     * Set custom columns for the admin list table
     */
    public function set_custom_columns($columns) {
        $new_columns = array();

        // Maintain checkbox for bulk actions
        if (isset($columns['cb'])) {
            $new_columns['cb'] = $columns['cb'];
        }

        // Add title column
        $new_columns['title'] = __('Title', 'advance-coupon');

        // Add store column
        $new_columns['store'] = __('Store', 'advance-coupon');

        // Add store categories column
        $new_columns['store_categories'] = __('Categories', 'advance-coupon');

        // Add status columns
        $new_columns['exclusive'] = __('Exclusive', 'advance-coupon');
        $new_columns['verified'] = __('Verified', 'advance-coupon');

        // Add expiration column
        $new_columns['expiration'] = __('Expires', 'advance-coupon');

        // Add date column
        if (isset($columns['date'])) {
            $new_columns['date'] = $columns['date'];
        }

        return $new_columns;
    }

    /**
     * Make columns sortable
     */
    public function sortable_columns($columns) {
        $columns['store'] = 'store';
        $columns['expiration'] = 'expiration';
        $columns['exclusive'] = 'exclusive';
        $columns['verified'] = 'verified';

        return $columns;
    }

    /**
     * Render custom column content
     */
    public function render_custom_columns($column, $post_id) {
        switch ($column) {
            case 'store':
                $store_id = get_post_meta($post_id, '_store_id', true);
                if (!empty($store_id)) {
                    $store = get_post($store_id);
                    if ($store) {
                        echo '<a href="' . get_edit_post_link($store_id) . '">' . esc_html($store->post_title) . '</a>';
                    } else {
                        echo '—';
                    }
                } else {
                    echo '—';
                }
                break;

            case 'exclusive':
                $is_exclusive = get_post_meta($post_id, '_is_exclusive', true);
                echo $is_exclusive ? '<span class="dashicons dashicons-yes" style="color: green;"></span>' : '<span class="dashicons dashicons-no" style="color: red;"></span>';
                break;

            case 'verified':
                $is_verified = get_post_meta($post_id, '_is_verified', true);
                echo $is_verified ? '<span class="dashicons dashicons-yes" style="color: green;"></span>' : '<span class="dashicons dashicons-no" style="color: red;"></span>';
                break;

            case 'expiration':
                $expire_time = get_post_meta($post_id, '_expire_time', true);
                if (!empty($expire_time)) {
                    $expire_date = new DateTime($expire_time);
                    $now = new DateTime();

                    if ($expire_date > $now) {
                        echo esc_html($expire_date->format('M d, Y'));
                    } else {
                        echo '<span style="color: red;">' . esc_html($expire_date->format('M d, Y')) . ' (Expired)</span>';
                    }
                } else {
                    echo '—';
                }
                break;

            case 'store_categories':
                $store_id = get_post_meta($post_id, '_store_id', true);
                if (!empty($store_id)) {
                    // Get store categories
                    $terms = wp_get_object_terms($store_id, 'store_category');
                    if (!is_wp_error($terms) && !empty($terms)) {
                        $term_links = array();
                        foreach ($terms as $term) {
                            $term_links[] = '<a href="edit.php?post_type=store&store_category=' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</a>';
                        }
                        echo implode(', ', $term_links);
                    } else {
                        echo '—';
                    }
                } else {
                    echo '—';
                }
                break;
        }
    }

    /**
     * Add admin filters
     */
    public function add_admin_filters($post_type) {
        global $pagenow, $wpdb;

        // Only add filters on the coupon list page
        if ('coupon' !== $post_type || 'edit.php' !== $pagenow) {
            return;
        }

        // Get current filter values
        $current_store = isset($_GET['coupon_store']) ? $_GET['coupon_store'] : '';
        $current_category = isset($_GET['coupon_category']) ? $_GET['coupon_category'] : '';
        $current_status = isset($_GET['coupon_status']) ? $_GET['coupon_status'] : '';

        // Get all unique store IDs used in coupons - using direct SQL query to avoid filter interference
        $store_ids = $wpdb->get_col(
            "SELECT DISTINCT meta_value FROM {$wpdb->postmeta}
            WHERE meta_key = '_store_id'
            AND meta_value != ''
            ORDER BY meta_value ASC"
        );

        // Now fetch the actual store posts - using get_posts instead of WP_Query to avoid filter interference
        $stores = array();
        if (!empty($store_ids)) {
            $stores = get_posts(array(
                'post_type' => 'store',
                'posts_per_page' => -1,
                'post__in' => $store_ids,
                'orderby' => 'title',
                'order' => 'ASC',
                'post_status' => array('publish', 'draft', 'pending', 'private'),
                'suppress_filters' => true, // Important: Prevent other filters from affecting this query
            ));
        }

        // Store filter
        echo '<label class="screen-reader-text" for="filter-by-store">' . __('Filter by store', 'advance-coupon') . '</label>';
        echo '<select name="coupon_store" id="filter-by-store">';
        echo '<option value="">' . __('All Stores', 'advance-coupon') . '</option>';

        foreach ($stores as $store) {
            printf(
                '<option value="%s" %s>%s</option>',
                $store->ID,
                selected($current_store, $store->ID, false),
                $store->post_title
            );
        }

        echo '</select>';

        // Store Category filter
        $categories = get_terms(array(
            'taxonomy' => 'store_category',
            'hide_empty' => false,
        ));

        if (!empty($categories) && !is_wp_error($categories)) {
            // Create the category dropdown
            echo '<label class="screen-reader-text" for="filter-by-category">' . __('Filter by category', 'advance-coupon') . '</label>';
            echo '<select name="store_category" id="filter-by-category">';
            echo '<option value="">' . __('All Categories', 'advance-coupon') . '</option>';

            foreach ($categories as $category) {
                printf(
                    '<option value="%s" %s>%s</option>',
                    $category->slug,
                    selected($current_category, $category->slug, false),
                    $category->name
                );
            }

            echo '</select>';
        }

        // Status filter
        echo '<label class="screen-reader-text" for="filter-by-status">' . __('Filter by status', 'advance-coupon') . '</label>';
        echo '<select name="coupon_status" id="filter-by-status">';
        echo '<option value="">' . __('All Status', 'advance-coupon') . '</option>';
        echo '<option value="exclusive" ' . selected($current_status, 'exclusive', false) . '>' . __('Exclusive Only', 'advance-coupon') . '</option>';
        echo '<option value="verified" ' . selected($current_status, 'verified', false) . '>' . __('Verified Only', 'advance-coupon') . '</option>';
        echo '<option value="both" ' . selected($current_status, 'both', false) . '>' . __('Exclusive & Verified', 'advance-coupon') . '</option>';
        echo '<option value="none" ' . selected($current_status, 'none', false) . '>' . __('Neither', 'advance-coupon') . '</option>';
        echo '</select>';

        // Add JavaScript to auto-submit when a filter changes
        echo '<script type="text/javascript">
            jQuery(document).ready(function($) {
                // Auto-submit when a filter changes
                $("#filter-by-store, #filter-by-category, #filter-by-status").on("change", function() {
                    var $form = $(this).closest("form");
                    $form.submit();
                });
            });
        </script>';
    }

    /**
     * Filter coupons by custom filters
     */
    public function filter_coupons_by_filters($query) {
        global $pagenow;

        // Check if we're in the admin area, on the post listing page
        if (!is_admin() || 'edit.php' !== $pagenow) {
            return;
        }

        // Make sure we're on the coupon post type
        if (!isset($_GET['post_type']) || 'coupon' !== $_GET['post_type']) {
            return;
        }

        // IMPORTANT: Only modify the main query to prevent interference with other queries
        if (!$query->is_main_query()) {
            return;
        }

        // Initialize meta query if not already set
        if (!isset($query->query_vars['meta_query'])) {
            $query->query_vars['meta_query'] = array();
        }

        // Filter by store
        if (isset($_GET['coupon_store']) && !empty($_GET['coupon_store'])) {
            $query->set('meta_key', '_store_id');
            $query->set('meta_value', sanitize_text_field($_GET['coupon_store']));
        }

        // Filter by store category
        if (isset($_GET['store_category']) && !empty($_GET['store_category'])) {
            // Get all stores with this category
            $stores = get_posts(array(
                'post_type' => 'store',
                'posts_per_page' => -1,
                'fields' => 'ids',
                'tax_query' => array(
                    array(
                        'taxonomy' => 'store_category',
                        'field'    => 'slug',
                        'terms'    => sanitize_text_field($_GET['store_category']),
                    ),
                ),
            ));

            if (!empty($stores)) {
                // Add meta query to filter coupons by these stores
                $query->query_vars['meta_query'][] = array(
                    'key' => '_store_id',
                    'value' => $stores,
                    'compare' => 'IN',
                );
            } else {
                // No stores found with this category, so no coupons will match
                $query->set('post__in', array(0)); // Force no results
            }
        }

        // Filter by status
        if (isset($_GET['coupon_status']) && !empty($_GET['coupon_status'])) {
            $meta_query = array();

            switch ($_GET['coupon_status']) {
                case 'exclusive':
                    $meta_query[] = array(
                        'key' => '_is_exclusive',
                        'value' => '1',
                        'compare' => '=',
                    );
                    break;

                case 'verified':
                    $meta_query[] = array(
                        'key' => '_is_verified',
                        'value' => '1',
                        'compare' => '=',
                    );
                    break;

                case 'both':
                    $meta_query = array(
                        'relation' => 'AND',
                        array(
                            'key' => '_is_exclusive',
                            'value' => '1',
                            'compare' => '=',
                        ),
                        array(
                            'key' => '_is_verified',
                            'value' => '1',
                            'compare' => '=',
                        ),
                    );
                    break;

                case 'none':
                    $meta_query = array(
                        'relation' => 'AND',
                        array(
                            'key' => '_is_exclusive',
                            'value' => '1',
                            'compare' => '!=',
                        ),
                        array(
                            'key' => '_is_verified',
                            'value' => '1',
                            'compare' => '!=',
                        ),
                    );
                    break;
            }

            // Set the meta query
            $query->set('meta_query', $meta_query);
        }
    }
}
