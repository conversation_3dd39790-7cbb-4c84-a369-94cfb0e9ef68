<?php
/**
 * AJAX handler for Advance Coupon
 */
class Advance_Coupon_Ajax {

    /**
     * Constructor
     */
    public function __construct() {
        // Register AJAX actions
        add_action('wp_ajax_load_store_widget_page', array($this, 'load_store_widget_page'));
        add_action('wp_ajax_nopriv_load_store_widget_page', array($this, 'load_store_widget_page'));

        // Register coupon usage tracking
        add_action('wp_ajax_track_coupon_usage', array($this, 'track_coupon_usage'));
        add_action('wp_ajax_nopriv_track_coupon_usage', array($this, 'track_coupon_usage'));

        // Register coupon click tracking
        add_action('wp_ajax_track_coupon_click', array($this, 'track_coupon_click'));
        add_action('wp_ajax_nopriv_track_coupon_click', array($this, 'track_coupon_click'));

        // Register coupon usage count getter
        add_action('wp_ajax_get_coupon_usage_count', array($this, 'get_coupon_usage_count'));
        add_action('wp_ajax_nopriv_get_coupon_usage_count', array($this, 'get_coupon_usage_count'));

        // Enqueue scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles
     */
    public function enqueue_scripts() {
        // Enqueue AJAX script
        wp_enqueue_script(
            'advance-coupon-store-widget-ajax',
            ADVCOUPON_PLUGIN_URL . 'assets/js/store-widget-ajax.js',
            array('jquery'),
            ADVCOUPON_VERSION,
            true
        );

        // Localize script with AJAX URL and nonce
        wp_localize_script(
            'advance-coupon-store-widget-ajax',
            'advance_coupon_ajax',
            array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('advance_coupon_ajax_nonce')
            )
        );

        // Enqueue CSS
        wp_enqueue_style(
            'advance-coupon-store-widget-ajax',
            ADVCOUPON_PLUGIN_URL . 'assets/css/store-widget-ajax.css',
            array(),
            ADVCOUPON_VERSION
        );
    }

    /**
     * AJAX handler for loading store widget pages
     */
    public function load_store_widget_page() {
        // Verify nonce
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'advance_coupon_ajax_nonce')) {
            wp_send_json_error(array('message' => 'Invalid security token.'));
        }

        // Get parameters
        $widget_id = isset($_POST['widget_id']) ? sanitize_text_field($_POST['widget_id']) : '';
        $page = isset($_POST['page']) ? intval($_POST['page']) : 1;
        $settings = isset($_POST['settings']) ? $_POST['settings'] : array();

        // Validate settings
        if (empty($settings) || !is_array($settings)) {
            wp_send_json_error(array('message' => 'Invalid widget settings.'));
        }

        // Sanitize settings
        $sanitized_settings = array();
        foreach ($settings as $key => $value) {
            if (is_array($value)) {
                $sanitized_settings[$key] = array_map('sanitize_text_field', $value);
            } else {
                $sanitized_settings[$key] = sanitize_text_field($value);
            }
        }

        // Start output buffering to capture the HTML
        ob_start();

        // Render the store widget content
        $this->render_store_widget_content($sanitized_settings, $page, $widget_id);

        // Get the buffered content
        $html = ob_get_clean();

        // Send the response
        wp_send_json_success(array('html' => $html));
    }

    /**
     * Render store widget content
     */
    private function render_store_widget_content($settings, $paged, $widget_id) {
        // Determine query parameters based on settings
        if ($settings['show_all_stores'] === 'yes') {
            // Archive mode: Show all stores with pagination
            $posts_per_page = $settings['pagination'] === 'yes' ? intval($settings['stores_per_page']) : 10; // Default to 10 if pagination is off
        } else {
            // Normal mode: Use pagination or limit setting
            if ($settings['pagination'] === 'yes') {
                $posts_per_page = intval($settings['stores_per_page']);
            } else {
                // This is where we use the Number of Stores setting
                $posts_per_page = intval($settings['limit']);

                // Debug: Add a comment to show the actual value being used
                // error_log('Using Number of Stores: ' . $posts_per_page);
            }
        }

        // Query arguments
        $args = array(
            'post_type' => 'store',
            'post_status' => 'publish',
            'posts_per_page' => $posts_per_page,
            'paged' => $paged,
            'meta_query' => array(),
        );

        // For archive mode, we don't want to limit the total number of posts
        if ($settings['show_all_stores'] === 'yes') {
            // This ensures we get the correct pagination
            $args['no_found_rows'] = false;
        }

        // Filter by network if specified
        if (!empty($settings['network']) && $settings['network'] !== 'all') {
            $args['meta_query'][] = array(
                'key' => '_store_network',
                'value' => $settings['network'],
                'compare' => '=',
            );
        }

        // Get stores
        $stores_query = new WP_Query($args);
        $stores = $stores_query->posts;

        // Output the widget title if set
        if (!empty($settings['title'])) {
            echo '<h3 class="store-widget-title">' . esc_html($settings['title']) . '</h3>';
        }

        // Output the stores grid
        echo '<div class="store-list columns-' . esc_attr($settings['columns']) . '">';

        if (!empty($stores)) {
            foreach ($stores as $store) {
                // Get store data
                $store_url = get_post_meta($store->ID, '_store_url', true);
                $store_network = get_post_meta($store->ID, '_store_network', true);
                $thumbnail = get_the_post_thumbnail($store->ID, 'store-thumbnail');

                echo '<div class="store-item">';

                if ($thumbnail) {
                    echo '<div class="store-thumbnail">';
                    echo '<a href="' . get_permalink($store->ID) . '">' . $thumbnail . '</a>';
                    echo '</div>';
                }

                // Get coupon count for this store
                $coupon_count = 0;
                $coupon_args = array(
                    'post_type' => 'coupon',
                    'post_status' => 'publish',
                    'posts_per_page' => -1,
                    'meta_query' => array(
                        array(
                            'key' => '_store_id',
                            'value' => $store->ID,
                            'compare' => '=',
                        ),
                    ),
                );
                $coupons = get_posts($coupon_args);
                $coupon_count = count($coupons);

                // Title and coupon count wrapper
                $wrapper_class = 'store-title-wrapper';
                if ($settings['show_coupon_count'] === 'yes' && $settings['badge_alignment'] === 'right') {
                    $wrapper_class .= ' badge-right-aligned';
                }

                echo '<div class="' . $wrapper_class . '">';
                echo '<h4 class="store-title"><a href="' . get_permalink($store->ID) . '">' . esc_html($store->post_title) . '</a></h4>';

                // Show coupon count badge if enabled
                if ($settings['show_coupon_count'] === 'yes') {
                    // Format the badge text
                    $badge_text = '';
                    if (!empty($settings['badge_text'])) {
                        $badge_text = sprintf($settings['badge_text'], $coupon_count);
                    } else {
                        $badge_text = $coupon_count;
                    }

                    echo '<span class="coupon-count-badge">' . $badge_text . '</span>';
                }

                echo '</div>'; // End of store-title-wrapper

                echo '<div class="store-actions">';
                echo '<a href="' . get_permalink($store->ID) . '" class="view-store">' . esc_html($settings['button_text']) . '</a>';
                echo '</div>';

                echo '</div>'; // End of store-item
            }
        } else {
            echo '<p>' . __('No stores found.', 'advance-coupon') . '</p>';
        }

        echo '</div>'; // End of store-grid

        // Add pagination if enabled and there are multiple pages
        if ($settings['pagination'] === 'yes' && $stores_query->max_num_pages > 1) {
            echo '<div class="store-pagination">';

            $big = 999999999; // need an unlikely integer

            // Get previous and next icons
            $prev_icon = '<i class="fas fa-chevron-left"></i>';
            $next_icon = '<i class="fas fa-chevron-right"></i>';

            // Pagination args
            $pagination_args = array(
                'base' => add_query_arg('store-page', '%#%'),
                'format' => '?store-page=%#%',
                'current' => max(1, $paged),
                'total' => $stores_query->max_num_pages,
                'prev_text' => $prev_icon,
                'next_text' => $next_icon,
                'type' => 'list',
            );

            // If display style is numbers only, remove prev/next
            if ($settings['pagination_display'] === 'numbers') {
                $pagination_args['prev_next'] = false;
            }

            // Generate the pagination links
            echo paginate_links($pagination_args);

            echo '</div>';
        }
    }

    /**
     * Track coupon usage
     */
    public function track_coupon_usage() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'advance_coupon_ajax_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
            return;
        }

        // Get coupon ID
        $coupon_id = isset($_POST['coupon_id']) ? intval($_POST['coupon_id']) : 0;

        if (!$coupon_id) {
            wp_send_json_error(array('message' => 'Invalid coupon ID'));
            return;
        }

        // Get current usage count
        $used_count = get_post_meta($coupon_id, '_used_count', true);
        $used_count = $used_count ? intval($used_count) : 0;

        // Increment usage count
        $used_count++;

        // Update usage count
        update_post_meta($coupon_id, '_used_count', $used_count);

        // Send success response
        wp_send_json_success(array(
            'message' => 'Coupon usage tracked successfully',
            'used_count' => $used_count
        ));
    }

    /**
     * Track coupon clicks
     */
    public function track_coupon_click() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'advance_coupon_ajax_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
            return;
        }

        // Get coupon ID
        $coupon_id = isset($_POST['coupon_id']) ? intval($_POST['coupon_id']) : 0;

        if (!$coupon_id) {
            wp_send_json_error(array('message' => 'Invalid coupon ID'));
            return;
        }

        // Get current views count
        $views_count = get_post_meta($coupon_id, '_views_count', true);
        $views_count = $views_count ? intval($views_count) : 0;

        // Increment views count
        $views_count++;

        // Update views count
        update_post_meta($coupon_id, '_views_count', $views_count);

        // Also increment usage count when clicked
        $used_count = get_post_meta($coupon_id, '_used_count', true);
        $used_count = $used_count ? intval($used_count) : 0;
        $used_count++;
        update_post_meta($coupon_id, '_used_count', $used_count);

        // Send success response
        wp_send_json_success(array(
            'message' => 'Coupon click tracked successfully',
            'views_count' => $views_count,
            'used_count' => $used_count
        ));
    }

    /**
     * Get coupon usage count
     */
    public function get_coupon_usage_count() {
        // Check nonce for security
        if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'advance_coupon_ajax_nonce')) {
            wp_send_json_error(array('message' => 'Invalid nonce'));
            return;
        }

        // Get coupon ID
        $coupon_id = isset($_POST['coupon_id']) ? intval($_POST['coupon_id']) : 0;

        if (!$coupon_id) {
            wp_send_json_error(array('message' => 'Invalid coupon ID'));
            return;
        }

        // Get current usage count
        $used_count = get_post_meta($coupon_id, '_used_count', true);
        $used_count = $used_count ? intval($used_count) : 0;

        // Send success response
        wp_send_json_success(array(
            'message' => 'Coupon usage count retrieved successfully',
            'used_count' => $used_count
        ));
    }
}

// Initialize the AJAX handler
new Advance_Coupon_Ajax();
