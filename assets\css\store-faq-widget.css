/**
 * Store FAQ Widget Styles
 */

/* Widget Container */
.store-faq-widget {
    margin-bottom: 30px;
}

.store-faq-widget-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}

/* FAQ Items */
.store-faq-items {
    width: 100%;
}

.store-faq-item {
    margin-bottom: 10px;
    overflow: hidden;
    transition: all 0.3s ease;
}

/* FAQ Header */
.store-faq-item-header {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: #f7f7f7;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
}

.store-faq-item-header:hover {
    background-color: #f0f0f0;
}

.store-faq-item-header.active {
    background-color: #e9e9e9;
}

/* Icon Positioning */
.icon-position-left {
    flex-direction: row;
}

.icon-position-right {
    flex-direction: row;
    justify-content: space-between;
}

.store-faq-item-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    transition: transform 0.3s ease;
}

/* Default CSS variable for icon spacing */
.elementor-widget-store_faq_widget {
    --faq-icon-spacing: 15px;
}

.store-faq-item-header.icon-position-left .store-faq-item-icon {
    margin-right: var(--faq-icon-spacing);
}

.store-faq-item-header.icon-position-right .store-faq-item-icon {
    margin-left: var(--faq-icon-spacing);
}

/* Question */
.store-faq-item-question {
    flex: 1;
    font-weight: 500;
}

/* Content/Answer */
.store-faq-item-content {
    background-color: #ffffff;
    padding: 15px;
    border-top: 0;
    display: none;
}

.store-faq-item-answer {
    line-height: 1.6;
}

.store-faq-item-answer p:last-child {
    margin-bottom: 0;
}

/* Error Messages */
.store-faq-error {
    padding: 20px;
    background-color: #f8f8f8;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    text-align: center;
    color: #666;
}

/* Responsive Styles */
@media (max-width: 767px) {
    .store-faq-widget-title {
        font-size: 20px;
    }

    .store-faq-item-header {
        padding: 12px;
    }

    .store-faq-item-content {
        padding: 12px;
    }
}
