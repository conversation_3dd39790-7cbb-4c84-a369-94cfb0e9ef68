<?php
namespace AdvanceCoupon\Elementor\Widgets;

if (!defined('ABSPATH')) {
    exit;
}

/**
 * Category Coupons Widget for Elementor
 *
 * This widget displays coupons for a specific category with a modern layout.
 */

class Category_Coupons_Widget extends \Elementor\Widget_Base {
    /**
     * Get widget name.
     *
     * @return string Widget name.
     */
    public function get_name() {
        return 'category-coupons';
    }

    /**
     * Get widget title.
     *
     * @return string Widget title.
     */
    public function get_title() {
        return __('Category Coupons', 'advance-coupon');
    }

    /**
     * Get widget icon.
     *
     * @return string Widget icon.
     */
    public function get_icon() {
        return 'eicon-posts-list';
    }
    /**
     * Get widget categories.
     *
     * @return array Widget categories.
     */
    public function get_categories() {
        return ['theme-elements', 'general'];
    }
    /**
     * Get widget keywords.
     *
     * @return array Widget keywords.
     */
    public function get_keywords() {
        return ['category', 'coupons', 'coupon', 'discount', 'sale', 'offer'];
    }    
    /**
     * Register widget controls.
     *
     * @return void
     */
    protected function register_controls() {
        // Content Section
        $this->start_controls_section(
            'content_section',
            [
                'label' => __('Content', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'category_id',
            [
                'label' => __('Category', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT2,
                'options' => $this->get_category_options(),
                'default' => $this->get_current_category_id(),
                'label_block' => true,
            ]
        );

        $this->add_control(
            'enable_pagination',
            [
                'label' => __('Enable Pagination', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'coupons_per_page',
            [
                'label' => __('Coupons per Page', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 10,
                'min' => 1,
                'step' => 1,
                'condition' => [
                    'enable_pagination' => 'yes',
                ],
            ]
        );

        // filter options
        $this->add_control(
            'filter_options_heading',
            [
                'label' => __('Filter Options', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'enable_filter',
            [
                'label' => __('Enable Filter', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_verified_filters',
            [
                'label' => __('Show Verified Filters', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'enable_filter' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_exclusive_filters',
            [
                'label' => __('Show Exclusive Filters', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'enable_filter' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'show_coupon_type_filters',
            [
                'label' => __('Show Coupon Type Filters', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
                'condition' => [
                    'enable_filter' => 'yes',
                ],
            ]
        );

        // Display Options
        $this->add_control(
            'display_options_heading',
            [
                'label' => __('Display Options', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'show_store_logos',
            [
                'label' => __('Show Store Logos', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_coupon_title',
            [
                'label' => __('Show Coupon Title', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_exclusive_badge',
            [
                'label' => __('Show Exclusive Badge', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_verified_badge',
            [
                'label' => __('Show Verified Badge', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_expiry_badge',
            [
                'label' => __('Show Expiry Badge', 'advance-coupon'),                
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->add_control(
            'show_expired_coupons',
            [                
                'label' => __('Show Expired Coupons', 'advance-coupon'),                
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
                'description' => __('Enable this only in Elementor editor to edit expired coupons. Disabled by default on the frontend.', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'show_button',
            [
                'label' => __('Show Button', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();

        // Style Section - Button Controls
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => __('Button', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        // Common Button Settings
        $this->add_control(
            'common_button_heading',
            [
                'label' => __('Common Button Settings', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'button_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-button' => '--button-padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => 12,
                    'right' => 25,
                    'bottom' => 12,
                    'left' => 25,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => 5,
                    'right' => 5,
                    'bottom' => 5,
                    'left' => 5,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]

        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'selector' => '{{WRAPPER}} .coupon-button',
                'fields_options' => [
                    'typography' => [
                        'default' => 'yes',
                    ],
                    'font_size' => [
                        'default' => [
                            'size' => 14,
                            'unit' => 'px',
                        ],
                        'responsive' => true,
                    ],
                    'font_weight' => [
                        'default' => '600',
                    ],
                    'line_height' => [
                        'responsive' => true,
                    ],
                    'letter_spacing' => [
                        'responsive' => true,
                    ],
                ],
            ]
        );

        // Get Deal Button Settings
        $this->add_control(
            'deal_button_heading',
            [
                'label' => __('Get Deal Button Settings', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'deal_button_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => '--deal-button-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'deal_button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => '--deal-button-color: {{VALUE}}; color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'deal_button_hover_bg_color',
            [
                'label' => __('Hover Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#339af0',
                'selectors' => [
                    '{{WRAPPER}} .deal-button:hover' => '--deal-button-hover-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        // Deal Button Icon Controls
        $this->add_control(
            'deal_button_icon_heading',
            [
                'label' => __('Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'deal_button_icon',
            [
                'label' => __('Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-arrow-right',
                    'library' => 'solid',
                ],
            ]
        );

        $this->add_control(
            'deal_button_icon_spacing',
            [
                'label' => __('Icon Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 8,
                ],
                'selectors' => [
                    '{{WRAPPER}} .deal-button i, {{WRAPPER}} .deal-button svg' => 'margin-left: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'deal_button_icon[value]!' => '',
                ],
            ]
        );

        $this->add_control(
            'deal_button_icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 6,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 12,
                ],
                'selectors' => [
                    '{{WRAPPER}} .deal-button i' => 'font-size: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .deal-button svg' => 'width: {{SIZE}}{{UNIT}}; height: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'deal_button_icon[value]!' => '',
                ],
            ]
        );

        // Deal Button Border Controls
        $this->add_control(
            'deal_button_border_heading',
            [
                'label' => __('Border', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'deal_button_border_style',
            [
                'label' => __('Border Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'none',
                'options' => [
                    'none' => __('None', 'advance-coupon'),
                    'solid' => __('Solid', 'advance-coupon'),
                    'dashed' => __('Dashed', 'advance-coupon'),
                    'dotted' => __('Dotted', 'advance-coupon'),
                    'double' => __('Double', 'advance-coupon'),
                ],
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => 'border-style: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'deal_button_border_width',
            [
                'label' => __('Border Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px'],
                'default' => [
                    'top' => 0,
                    'right' => 0,
                    'bottom' => 0,
                    'left' => 0,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => 'border-width: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'deal_button_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'deal_button_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => 'border-color: {{VALUE}};',
                ],
                'condition' => [
                    'deal_button_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'deal_button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'default' => [
                    'top' => 50,
                    'right' => 50,
                    'bottom' => 50,
                    'left' => 50,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .deal-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        // Print Code Button Settings
        $this->add_control(
            'print_button_heading',
            [
                'label' => __('Print Code Button', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'print_button_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => 'transparent',
                'selectors' => [
                    '{{WRAPPER}} .print-button' => '--print-button-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'print_button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .print-button' => '--print-button-color: {{VALUE}}; color: {{VALUE}};',
                ],
            ]
        );


        // Print Button Icon Controls
        $this->add_control(
            'print_button_icon_heading',
            [
                'label' => __('Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'print_button_icon',
            [
                'label' => __('Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-arrow-right',
                    'library' => 'solid',
                ],
            ]
        );

        $this->add_control(
            'print_button_icon_spacing',
            [
                'label' => __('Icon Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 8,
                ],
                'selectors' => [
                    '{{WRAPPER}} .print-button i, {{WRAPPER}} .print-button svg' => 'margin-left: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'print_button_icon[value]!' => '',
                ],
            ]
        );

        $this->add_control(
            'print_button_icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 6,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 12,
                ],
                'selectors' => [
                    '{{WRAPPER}} .print-button i' => 'font-size: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .print-button svg' => 'width: {{SIZE}}{{UNIT}}; height: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'print_button_icon[value]!' => '',
                ],
            ]
        );

       // Print Button Border Controls
       $this->add_control(
        'print_button_border_heading',
        [
            'label' => __('Border', 'advance-coupon'),
            'type' => \Elementor\Controls_Manager::HEADING,
            'separator' => 'before',
        ]
        );

        $this->add_control(
            'print_button_border_style',
            [
                'label' => __('Border Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'dashed',
                'options' => [
                    'none' => __('None', 'advance-coupon'),
                    'solid' => __('Solid', 'advance-coupon'),
                    'dashed' => __('Dashed', 'advance-coupon'),
                    'dotted' => __('Dotted', 'advance-coupon'),
                    'double' => __('Double', 'advance-coupon'),
                ],
                'selectors' => [
                    '{{WRAPPER}} .print-button' => 'border-style: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'print_button_border_width',
            [
                'label' => __('Border Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px'],
                'default' => [
                    'top' => 1,
                    'right' => 1,
                    'bottom' => 1,
                    'left' => 1,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .print-button' => 'border-width: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'print_button_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'print_button_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .print-button' => 'border-color: {{VALUE}};',
                ],
                'condition' => [
                    'print_button_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'print_button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 50,
                    'right' => 50,
                    'bottom' => 50,
                    'left' => 50,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .print-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'print_button_hover_bg_color',
            [
                'label' => __('Hover Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => 'rgba(77, 171, 247, 0.1)',
                'selectors' => [
                    '{{WRAPPER}} .print-button:hover' => '--print-button-hover-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        // Get Code Button Settings
        $this->add_control(
            'code_button_heading',
            [
                'label' => __('Get Code Button', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'code_button_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#5B9EFF',
                'selectors' => [
                    '{{WRAPPER}} .code-button' => '--code-button-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'code_button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .code-button' => '--code-button-color: {{VALUE}}; color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'code_button_hover_bg_color',
            [
                'label' => __('Hover Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4a8cff',
                'selectors' => [
                    '{{WRAPPER}} .code-button:hover' => '--code-button-hover-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        // Code Preview Settings
        $this->add_control(
            'code_preview_heading',
            [
                'label' => __('Code Preview', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'code_preview_width',
            [
                'label' => __('Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['%'],
                'range' => [
                    '%' => [
                        'min' => 50,
                        'max' => 100,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => '%',
                    'size' => 80,
                ],
                'selectors' => [
                    '{{WRAPPER}} .code-preview' => '--code-preview-width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'code_preview_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => 'transparent',
                'selectors' => [
                    '{{WRAPPER}} .code-preview' => '--code-preview-bg: {{VALUE}}; background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'code_preview_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#5B9EFF',
                'selectors' => [
                    '{{WRAPPER}} .code-preview' => '--code-preview-color: {{VALUE}}; color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'code_preview_border_style',
            [
                'label' => __('Border Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'dashed',
                'options' => [
                    'solid' => __('Solid', 'advance-coupon'),
                    'dashed' => __('Dashed', 'advance-coupon'),
                    'dotted' => __('Dotted', 'advance-coupon'),
                    'double' => __('Double', 'advance-coupon'),
                ],
            ]
        );

        $this->add_control(
            'code_preview_border_width',
            [
                'label' => __('Border Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 10,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 2,
                ],
            ]
        );

        $this->add_control(
            'code_preview_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#5B9EFF',
            ]
        );

        // Combine border properties
        $this->add_control(
            'code_preview_border',
            [
                'label' => __('Border', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HIDDEN,
                'default' => '2px dashed #5B9EFF',
                'selectors' => [
                    '{{WRAPPER}} .code-preview' => '--code-preview-border: {{code_preview_border_width.SIZE}}{{code_preview_border_width.UNIT}} {{code_preview_border_style.VALUE}} {{code_preview_border_color.VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'code_preview_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 0,
                    'right' => 50,
                    'bottom' => 50,
                    'left' => 0,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '{{WRAPPER}} .code-preview' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'code_preview_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .code-preview',
                'fields_options' => [
                    'font_family' => [
                        'default' => 'monospace',
                    ],
                    'font_weight' => [
                        'default' => 'bold',
                    ],
                    'font_size' => [
                        'default' => [
                            'unit' => 'px',
                            'size' => 14,
                        ],
                        'responsive' => true,
                    ],
                    'line_height' => [
                        'responsive' => true,
                    ],
                    'letter_spacing' => [
                        'responsive' => true,
                    ],
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Button
        $this->start_controls_section(
            'button_style_section',
            [
                'label' => __('Button', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'button_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .coupon-button' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'button_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .coupon-button' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'button_typography',
                'selector' => '{{WRAPPER}} .coupon-button',
            ]
        );

        $this->add_control(
            'button_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '4',
                    'right' => '4',
                    'bottom' => '4',
                    'left' => '4',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Popup
        $this->start_controls_section(
            'popup_style_section',
            [
                'label' => __('Popup', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'popup_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .coupon-popup' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-popup' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'default' => [
                    'top' => '8',
                    'right' => '8',
                    'bottom' => '8',
                    'left' => '8',
                    'unit' => 'px',
                    'isLinked' => true,
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'popup_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-popup',
            ]
        );

        $this->end_controls_section();

        // Style Section - Zigzag Pattern
        $this->start_controls_section(
            'zigzag_style_section',
            [
                'label' => __('Zigzag Pattern', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'enable_zigzag',
            [
                'label' => __('Enable Zigzag Pattern', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );
        
        $this->add_responsive_control(
            'zigzag_size',
            [
                'label' => __('Zigzag Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 5,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 20,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-coupon-item' => '--zigzag-size: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'enable_zigzag' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'zigzag_density',
            [
                'label' => __('Zigzag Density', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => '10',
                'options' => [
                    '2' => __('2 points', 'advance-coupon'),
                    '3' => __('3 points', 'advance-coupon'),
                    '4' => __('4 points', 'advance-coupon'),
                    '5' => __('5 points', 'advance-coupon'),
                    '6' => __('6 points', 'advance-coupon'),
                    '8' => __('8 points', 'advance-coupon'),
                    '10' => __('10 points', 'advance-coupon'),
                    '12' => __('12 points', 'advance-coupon'),
                    '15' => __('15 points', 'advance-coupon'),
                    '20' => __('20 points', 'advance-coupon'),
                    '25' => __('25 points', 'advance-coupon'),
                    '30' => __('30 points', 'advance-coupon'),
                    '40' => __('40 points', 'advance-coupon'),
                ],
                'condition' => [
                    'enable_zigzag' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'zigzag_sides',
            [
                'label' => __('Zigzag Sides', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'right',
                'options' => [
                    'right' => __('Right Side Only', 'advance-coupon'),
                    'all' => __('All Sides', 'advance-coupon'),
                ],
                'condition' => [
                    'enable_zigzag' => 'yes',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Coupon Title
        $this->start_controls_section(
            'coupon_title_style_section',
            [
                'label' => __('Coupon Title', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'coupon_title_color',
            [
                'label' => __('Title Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '{{WRAPPER}} .coupon-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'coupon_title_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-title',
                'fields_options' => [
                    'font_weight' => [
                        'default' => '600',
                    ],
                    'font_size' => [
                        'default' => [
                            'unit' => 'px',
                            'size' => 16,
                        ],
                        'responsive' => true,
                    ],
                    'line_height' => [
                        'responsive' => true,
                    ],
                    'letter_spacing' => [
                        'responsive' => true,
                    ],
                ],
            ]
        );

        $this->add_responsive_control(
            'coupon_title_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .coupon-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Badges
        $this->start_controls_section(
            'badges_style_section',
            [
                'label' => __('Badges', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        // Exclusive Badge
        $this->add_control(
            'exclusive_badge_heading',
            [
                'label' => __('Exclusive Badge', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
            ]
        );

        $this->add_control(
            'exclusive_badge_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#e0f2ff',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.exclusive' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'exclusive_badge_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#1976d2',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.exclusive' => 'color: {{VALUE}};',
                ],
            ]
        );

        // Verified Badge
        $this->add_control(
            'verified_badge_heading',
            [
                'label' => __('Verified Badge', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'verified_badge_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#def9fa',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.verified' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'verified_badge_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#0097a7',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.verified' => 'color: {{VALUE}};',
                ],
            ]
        );

        // Expiry Badge
        $this->add_control(
            'expiry_badge_heading',
            [
                'label' => __('Expiry Badge', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'expiry_badge_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#e8f5e9',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.expiry' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'expiry_badge_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#388e3c',
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge.expiry' => 'color: {{VALUE}};',
                ],
            ]
        );

        // Common Badge Styles
        $this->add_control(
            'common_badge_heading',
            [
                'label' => __('Common Badge Styles', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'badge_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'default' => [
                    'top' => 4,
                    'right' => 4,
                    'bottom' => 4,
                    'left' => 4,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'badge_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 4,
                    'right' => 8,
                    'bottom' => 4,
                    'left' => 8,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'badge_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 0,
                    'right' => 4,
                    'bottom' => 4,
                    'left' => 0,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '{{WRAPPER}} .coupon-badge' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'badge_typography',
                'label' => __('Typography', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-badge',
                'fields_options' => [
                    'font_size' => [
                        'default' => [
                            'unit' => 'px',
                            'size' => 12,
                        ],
                        'responsive' => true,
                    ],
                    'font_weight' => [
                        'default' => '500',
                    ],
                    'line-height' => [
                        'responsive' => true,
                    ],
                    'letter-spacing' => [
                        'responsive' => true,
                    ],
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'badge_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-badge',
            ]
        );

        $this->end_controls_section();

        // Style Section - Popup Controls
        $this->start_controls_section(
            'popup_style_section',
            [
                'label' => __('Popup Styles', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'popup_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '.coupon-popup-content' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '.coupon-popup-content' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_heading_color',
            [
                'label' => __('Heading Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '.coupon-popup-content h3' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 8,
                    'right' => 8,
                    'bottom' => 8,
                    'left' => 8,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '.coupon-popup-content' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'popup_box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .coupon-popup-content',
            ]
        );

        $this->add_responsive_control(
            'popup_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 30,
                    'right' => 30,
                    'bottom' => 30,
                    'left' => 30,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '.coupon-popup-content' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        // Popup Code Display
        $this->add_control(
            'popup_code_heading',
            [
                'label' => __('Code Display', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'popup_code_background_color',
            [
                'label' => __('Code Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#f5f5f5',
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_code_text_color',
            [
                'label' => __('Code Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#333333',
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_code_border_style',
            [
                'label' => __('Code Border Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'dashed',
                'options' => [
                    'none' => __('None', 'advance-coupon'),
                    'solid' => __('Solid', 'advance-coupon'),
                    'dashed' => __('Dashed', 'advance-coupon'),
                    'dotted' => __('Dotted', 'advance-coupon'),
                    'double' => __('Double', 'advance-coupon'),
                ],
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'border-style: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_code_border_width',
            [
                'label' => __('Code Border Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px'],
                'default' => [
                    'top' => 1,
                    'right' => 1,
                    'bottom' => 1,
                    'left' => 1,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'border-width: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'popup_code_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'popup_code_border_color',
            [
                'label' => __('Code Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#dddddd',
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'border-color: {{VALUE}};',
                ],
                'condition' => [
                    'popup_code_border_style!' => 'none',
                ],
            ]
        );

        $this->add_control(
            'popup_code_border_radius',
            [
                'label' => __('Code Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 4,
                    'right' => 4,
                    'bottom' => 4,
                    'left' => 4,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'popup_code_padding',
            [
                'label' => __('Code Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 10,
                    'right' => 15,
                    'bottom' => 10,
                    'left' => 15,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '.coupon-popup-content .coupon-code' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'popup_code_typography',
                'label' => __('Code Typography', 'advance-coupon'),
                'selector' => '.coupon-popup-content .coupon-code',
                'fields_options' => [
                    'font_family' => [
                        'default' => 'monospace',
                    ],
                    'font_weight' => [
                        'default' => 'bold',
                    ],
                    'font_size' => [
                        'default' => [
                            'unit' => 'px',
                            'size' => 18,
                        ],
                        'responsive' => true,
                    ],
                    'line_height' => [
                        'responsive' => true,
                    ],
                    'letter_spacing' => [
                        'responsive' => true,
                    ],
                ],
            ]
        );

        // Popup Button
        $this->add_control(
            'popup_button_heading',
            [
                'label' => __('Copy Button', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'popup_button_background_color',
            [
                'label' => __('Button Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '.coupon-popup-content .copy-button' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_button_text_color',
            [
                'label' => __('Button Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '.coupon-popup-content .copy-button' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_button_hover_background_color',
            [
                'label' => __('Button Hover Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#339af0',
                'selectors' => [
                    '.coupon-popup-content .copy-button:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_button_hover_text_color',
            [
                'label' => __('Button Hover Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '.coupon-popup-content .copy-button:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'popup_button_border_radius',
            [
                'label' => __('Button Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 4,
                    'right' => 4,
                    'bottom' => 4,
                    'left' => 4,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '.coupon-popup-content .copy-button' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'popup_button_padding',
            [
                'label' => __('Button Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 10,
                    'right' => 20,
                    'bottom' => 10,
                    'left' => 20,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '.coupon-popup-content .copy-button' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'popup_button_typography',
                'label' => __('Button Typography', 'advance-coupon'),
                'selector' => '.coupon-popup-content .copy-button',
            ]
        );

        $this->end_controls_section();


        // Style Section - Filters
        $this->start_controls_section(
            'filter_style_section',
            [
                'label' => __('Filters', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'enable_filter' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'filter_spacing',
            [
                'label' => __('Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 20,
                ],
                'selectors' => [
                    '{{WRAPPER}} .coupon-filters' => 'margin-bottom: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        // Start tabs for Normal, Hover, Active states
        $this->start_controls_tabs('filter_style_tabs');

        // Normal state tab
        $this->start_controls_tab(
            'filter_style_normal',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'filter_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#f8f9fa',
                'selectors' => [
                    '{{WRAPPER}} .filter-item' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'filter_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#495057',
                'selectors' => [
                    '{{WRAPPER}} .filter-item' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'filter_typography',
                'selector' => '{{WRAPPER}} .filter-item',
                'fields_options' => [
                    'font_size' => [
                        'responsive' => true,
                    ],
                    'line_height' => [
                        'responsive' => true,
                    ],
                    'letter_spacing' => [
                        'responsive' => true,
                    ],
                ],

            ]
        );

        $this->add_control(
            'filter_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 30,
                    'right' => 30,
                    'bottom' => 30,
                    'left' => 30,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .filter-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'filter_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 8,
                    'right' => 15,
                    'bottom' => 8,
                    'left' => 15,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '{{WRAPPER}} .filter-item' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state tab
        $this->start_controls_tab(
            'filter_style_hover',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'filter_background_color_hover',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#e9ecef',
                'selectors' => [
                    '{{WRAPPER}} .filter-item:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'filter_text_color_hover',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#212529',
                'selectors' => [
                    '{{WRAPPER}} .filter-item:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Active state tab
        $this->start_controls_tab(
            'filter_style_active',
            [
                'label' => __('Active', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'filter_background_color_active',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .filter-item.active' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'filter_text_color_active',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .filter-item.active' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'filter_Box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .filter-item',
                'fields_options' => [
                    'box_shadow_type' => [
                        'default' => 'yes',
                    ],
                    'box_shadow' => [
                        'default' => [
                            'horizontal' => 5,
                            'vertical' => 5,
                            'blur' => 10,
                            'spread' => -10,
                            'color' => 'rgba(0,0,0,0.5)',
                        ],
                    ],
                ],
            ]
        );

        $this->end_controls_section();

        // Style Section - Pagination
        $this->start_controls_section(
            'pagination_style_section',
            [
                'label' => __('Pagination', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'enable_pagination' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'pagination_spacing',
            [
                'label' => __('Spacing', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 30,
                ],
                'selectors' => [
                    '{{WRAPPER}} .coupon-pagination' => 'margin-top: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_alignment',
            [
                'label' => __('Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'flex-start' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'flex-end' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'center',
                'selectors' => [
                    '{{WRAPPER}} .coupon-pagination' => 'justify-content: {{VALUE}};',
                ],
            ]
        );

        // Start tabs for Normal, Hover, Active states
        $this->start_controls_tabs('pagination_style_tabs');

        // Normal state tab
        $this->start_controls_tab(
            'pagination_style_normal',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#f8f9fa',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_text_color',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#495057',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'pagination_typography',
                'selector' => '{{WRAPPER}} .pagination-item',
            ]
        );

        $this->add_control(
            'pagination_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'default' => [
                    'top' => 4,
                    'right' => 4,
                    'bottom' => 4,
                    'left' => 4,
                    'unit' => 'px',
                    'isLinked' => true,
                ],
                'selectors' => [
                    '{{WRAPPER}} .pagination-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'default' => [
                    'top' => 8,
                    'right' => 15,
                    'bottom' => 8,
                    'left' => 15,
                    'unit' => 'px',
                    'isLinked' => false,
                ],
                'selectors' => [
                    '{{WRAPPER}} .pagination-item' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Hover state tab
        $this->start_controls_tab(
            'pagination_style_hover',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_background_color_hover',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#e9ecef',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_text_color_hover',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#212529',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        // Active state tab
        $this->start_controls_tab(
            'pagination_style_active',
            [
                'label' => __('Active', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_background_color_active',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#4dabf7',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item.active' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_text_color_active',
            [
                'label' => __('Text Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '#ffffff',
                'selectors' => [
                    '{{WRAPPER}} .pagination-item.active' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        // Pagination Icon Size
        $this->add_control(
            'pagination_icon_heading',
            [
                'label' => __('Pagination Icons', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HEADING,
                'separator' => 'before',
            ]
        );

        $this->add_control(
            'pagination_icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 8,
                        'max' => 30,
                        'step' => 1,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 12,
                ],
                'selectors' => [
                    '{{WRAPPER}} .pagination-item i' => 'font-size: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

    }

    /**
     * Get category options for select control
     */
    protected function get_category_options() {
        $options = [
            'current' => __('Current Category', 'advance-coupon'),
        ];

        $categories = get_categories([
            'taxonomy' => 'store_category',
            'hide_empty' => false,
        ]);

        foreach ($categories as $category) {
            $options[$category->term_id] = $category->name;
        }

        return $options;
    }

    /**
     * Get current category ID
     */
    protected function get_current_category_id() {
        $current_category = get_queried_object();

        if ($current_category && isset($current_category->term_id)) {
            return $current_category->term_id;
        }

        return 'current';

    }

    /**
     * Render widget output on the frontend.
     *
     * @return void
     */
    protected function render() {
        $settings = $this->get_settings_for_display();
        $widget_id = 'category-coupons-'. $this->get_id();
        $category_id = $settings['category_id'];

        // Get filter parameters from URL
        $current_filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : '';
        $current_page = isset($_GET['coupon_page']) ? intval($_GET['coupon_page']) : 1;

        // Display filters if enabled
        if ($settings['enable_filter'] === 'yes') {
            echo '<div class="coupon-filters">';
            echo '<ul class="filter-list">';

            // All filter
            $all_class = empty($current_filter) ? 'active' : '';
            echo '<li><a href="' . esc_url(remove_query_arg(['filter', 'coupon_page'])) . '" class="filter-item ' . $all_class . '">' . __('All', 'advance-coupon') . '</a></li>';

            // Verified filter
            if ($settings['show_verified_filters'] === 'yes') {
                $verified_class = ($current_filter === 'verified') ? 'active' : '';
                echo '<li><a href="' . esc_url(add_query_arg(['filter' => 'verified'], remove_query_arg('coupon_page'))) . '" class="filter-item ' . $verified_class . '">' . __('Verified', 'advance-coupon') . '</a></li>';
            }

            // Exclusive filter
            if ($settings['show_exclusive_filters'] === 'yes') {
                $exclusive_class = ($current_filter === 'exclusive') ? 'active' : '';
                echo '<li><a href="' . esc_url(add_query_arg(['filter' => 'exclusive'], remove_query_arg('coupon_page'))) . '" class="filter-item ' . $exclusive_class . '">' . __('Exclusive', 'advance-coupon') . '</a></li>';
            }

            // Coupon type filters
            if ($settings['show_coupon_type_filters'] === 'yes') {
                $code_class = ($current_filter === 'code') ? 'active' : '';
                echo '<li><a href="' . esc_url(add_query_arg(['filter' => 'code'], remove_query_arg('coupon_page'))) . '" class="filter-item ' . $code_class . '">' . __('Codes', 'advance-coupon') . '</a></li>';

                $deal_class = ($current_filter === 'deal') ? 'active' : '';
                echo '<li><a href="' . esc_url(add_query_arg(['filter' => 'deal'], remove_query_arg('coupon_page'))) . '" class="filter-item ' . $deal_class . '">' . __('Deals', 'advance-coupon') . '</a></li>';
            }

            echo '</ul>';
            echo '</div>';
        }

        $store_ids = $this->get_store_info_by_category($category_id);

        if (empty($store_ids)) {
            echo '<div class="store-coupons-error">' . __('No stores found in this category.', 'advance-coupon') . '</div>';
            return;
        }

        // Query coupons from these stores
        $args = array(
            'post_type' => 'coupon',
            'posts_per_page' => $settings['enable_pagination'] === 'yes' ? $settings['coupons_per_page'] : -1,
            'paged' => isset($_GET['coupon_page']) ? intval($_GET['coupon_page']) : 1,
            'meta_query' => array(
                array(
                    'key' => '_store_id',
                    'value' => $store_ids,
                    'compare' => 'IN',
                ),
            ),
        );

        // Apply filter if set
        $current_filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : '';
        if (!empty($current_filter)) {
            switch ($current_filter) {
                case 'verified':
                    $args['meta_query'][] = array(
                        'key' => '_is_verified',
                        'value' => '1',
                        'compare' => '=',
                    );
                    break;
                case 'exclusive':
                    $args['meta_query'][] = array(
                        'key' => '_is_exclusive',
                        'value' => '1',
                        'compare' => '=',
                    );
                    break;
                case 'code':
                    $args['meta_query'][] = array(
                        'key' => '_coupon_type',
                        'value' => 'online_code',
                        'compare' => '=',
                    );
                    break;
                case 'deal':
                    $args['meta_query'][] = array(
                        'key' => '_coupon_type',
                        'value' => 'online_sale',
                        'compare' => '=',
                    );
                    break;
            }
        }

        // Add expiry filter
        if ($settings['show_expired_coupons'] !== 'yes' && !\Elementor\Plugin::$instance->editor->is_edit_mode()) {
            $now = current_time('mysql');
            $args['meta_query'][] = [
                'relation' => 'OR',
                [
                    'key' => '_expire_time',
                    'value' => $now,
                    'compare' => '>',
                    'type' => 'DATETIME'
                ],
                [
                    'key' => '_expire_time',
                    'value' => '',
                    'compare' => '='
                ],
                [
                    'key' => '_expire_time',
                    'compare' => 'NOT EXISTS'
                ]
            ];
        }

        $coupons_query = new \WP_Query($args);
        $coupons_info = $coupons_query->posts;

        if (empty($coupons_info)) {
            echo '<div class="store-coupons-error">' . __('No coupons found for this category.', 'advance-coupon') . '</div>';
            return;
        }

        // Set zigzag class based on settings
        $zigzag_class = '';
        if($settings['enable_zigzag'] === 'yes'){
            $zigzag_class = 'has-zigzag ' . ($settings['zigzag_sides'] === 'all' ? 'zigzag-all-sides' : 'zigzag-right-side');
            $zigzag_class .= ' zigzag-density-' . $settings['zigzag_density'];
        }

        // Start output
        $output = '<div id="' . esc_attr($widget_id) . '" class="store-coupons-container '. esc_attr($zigzag_class) . '">';

        foreach ($coupons_info as $coupon) {
            // Get coupon data
            $coupon_type = get_post_meta($coupon->ID, '_coupon_type', true);
            $expire_time = get_post_meta($coupon->ID, '_expire_time', true);
            $is_exclusive = get_post_meta($coupon->ID, '_is_exclusive', true);
            $is_verified = get_post_meta($coupon->ID, '_is_verified', true);
            $code = get_post_meta($coupon->ID, '_code', true);
            $coupon_link = get_post_meta($coupon->ID, '_coupon_link', true);
            $specific_link = get_post_meta($coupon->ID, '_specific_link', true);
            $coupon_views = get_post_meta($coupon->ID, '_views_count', true) ? get_post_meta($coupon->ID, '_views_count', true) : 0;

            $store_id = get_post_meta($coupon->ID, '_store_id', true);
            $store = get_post($store_id);
            $store_logo = get_the_post_thumbnail_url($store, 'medium');
            $store_url = get_post_meta($store_id, '_store_url', true);

            // Format expiry date
            $formatted_date = '';
            $is_expired = false;
            if (!empty($expire_time)) {
                $expire_date = new \DateTime($expire_time);
                $now = new \DateTime();
                if ($expire_date < $now) {
                    $formatted_date = $expire_date->format('M d, Y');
                    $is_expired = true;
                } else {
                    $formatted_date = $expire_date->format('M d, Y');
                }
            }

            // Determine button action, link and text based on coupon type
            $button_action = 'window';
            $button_link = '';
            $button_class = 'coupon-button';
            $button_text = '';

            if ($coupon_type === 'online_code' && !empty($code)) {
                $button_action = 'popup';
                $button_link = !empty($specific_link) ? $specific_link : $store_url;
                $button_text = 'GET CODE';
                $button_class = 'coupon-button code-button';
            } else if ($coupon_type === 'online_sale') {
                $button_link = !empty($coupon_link) ? $coupon_link : $store_url;
                $button_text = 'GET DEAL';
                $button_class = 'coupon-button deal-button';
            } else { // in_store_code
                $button_link = $store_url;
                $button_text = 'print code';
                $button_class = 'coupon-button print-button';
            }

            $output .= '<div class="store-coupon-item-wrapper"><div class="store-coupon-item">';
            $output .= '<div class="store-coupon-layout">';

            if($settings['show_store_logos'] === 'yes'){
                $output .= '<div class="store-logo">';
                if($store_logo){
                    $output .= '<img src="' . esc_url($store_logo) . '" alt="' . esc_attr($store->post_title) . '" />';
                }
                $output .= '</div>';
            }

            // Coupon content
            $output .= '<div class="coupon-content">';
            
            // Badges
            $output .= '<div class="coupon-badges">';
            if($is_exclusive === '1' && $settings['show_exclusive_badge'] === 'yes'){
                $output .= '<span class="coupon-badge exclusive">'. esc_html__('Exclusive', 'advance-coupon') . '</span>';
            }

            if($is_verified === '1' && $settings['show_verified_badge'] === 'yes'){
                $output .= '<span class="coupon-badge verified"><i class="fas fa-check-circle"></i>'. esc_html__('Verified', 'advance-coupon') . '</span>';
            }

            if(!empty($formatted_date) && $settings['show_expiry_badge'] === 'yes'){
                $output .= '<span class="coupon-badge expiry"><i class="fas fa-calendar-alt"></i>'. esc_html__('Expires: ', 'advance-coupon') . ' ' . esc_html($formatted_date) . '</span>';
            }
            $output .= '</div>';

            // Title
            if($settings['show_coupon_title'] === 'yes'){
                $output .= '<h3 class="coupon-title">' . esc_html($coupon->post_title) . '</h3>';
            }

            // Meta
            $output .= '<div class="coupon-meta">';
            $output .= '<div class="coupon-id-wrapper">';
            $output .= '<i class="fas fa-lock"></i> <span class="coupon-id">' . esc_html($coupon->ID) . '</span>';
            $output .= '</div>';

            $output .= '<div class="coupon-actions">';
            $output .= '<span class="coupon-views">' . esc_html($coupon_views) . '</span>';
            $output .= '</div>';
            $output .= '</div>';

            $output .= '</div>';

            // Button
            if($settings['show_button'] === 'yes'){
                $output .= '<div class="coupon-button-wrapper">';
                
                // For online code, create a special button with code preview
                if ($coupon_type === 'online_code' && !empty($code)){
                    $output .= '<div class="code-button-container">';
                    $output .= '<button class="' . esc_attr($button_class) . ' coupon-btn" ';
                    $output .= 'data-action="' . esc_attr($button_action) . '" ';
                    $output .= 'data-link="' . esc_url($button_link) . '" ';
                    $output .= 'data-code="' . esc_attr($code) . '"';
                    $output .= 'data-coupon-id="' . esc_attr($coupon->ID) . '"';
                    $output .= 'data-store-id="' . esc_attr($store_id) . '" ';
                    $output .= 'data-store-logo="' . esc_url($store_logo) . '" ';
                    $output .= 'data-store-name="' . esc_attr($store->post_title) . '" ';
                    $output .= 'data-coupon-title="' . esc_attr($coupon->post_title) . '">';
                    $output .= esc_html($button_text);
                    $output .= '</button>';

                    // Add code preview part - show only the last 2 characters of the code
                    $code_length = strlen($code);
                    $masked_code = '';

                    if($code_length > 2){
                        $masked_code = str_repeat('*', $code_length - 2) . substr($code, -2);
                    } else {
                        $masked_code = $code;
                    }

                    $output .= '<div class="code-preview">' . esc_html($masked_code) . '</div>';
                    $output .= '</div>'; // End code-button-container
                } else if($coupon_type === 'online_sale'){
                    // For deal button
                    $output .= '<button class="' . esc_attr($button_class) . '" ';
                    $output .= 'data-action="' . esc_attr($button_action) . '" ';
                    $output .= 'data-link="' . esc_url($button_link) . '" ';
                    $output .= 'data-code="' . esc_attr($code) . '" ';
                    $output .= 'data-coupon-id="' . esc_attr($coupon->ID) . '" ';
                    $output .= 'data-store-id="' . esc_attr($store_id) . '" ';
                    $output .= 'data-store-logo="' . esc_url($store_logo) . '" ';
                    $output .= 'data-store-name="' . esc_attr($store->post_title) . '" ';
                    $output .= 'data-coupon-title="' . esc_attr($coupon->post_title) . '">';
                    $output .= esc_html($button_text);

                    // Add icon if set
                    $deal_icon = $settings['deal_button_icon'];
                    if (!empty($deal_icon['value'])) {
                        $output .= ' <span class="button-icon">';
                        if (is_array($deal_icon) && !empty($deal_icon['value'])) {
                            // For SVG icons
                            if (isset($deal_icon['library']) && $deal_icon['library'] === 'svg') {
                                $output .= $deal_icon['value'];
                            } else {
                                // For Font Awesome icons
                                $output .= '<i class="' . esc_attr($deal_icon['value']) . '"></i>';
                            }
                        }
                        $output .= '</span>';
                    } else {
                        // Default icon if none set
                        $output .= ' <i class="fas fa-arrow-right"></i>';
                    }
                    $output .= '</button>';
                } else {
                    // For print button
                    $output .= '<button class="' . esc_attr($button_class) . '" ';
                    $output .= 'data-action="' . esc_attr($button_action) . '" ';
                    $output .= 'data-link="' . esc_url($button_link) . '" ';
                    $output .= 'data-code="' . esc_attr($code) . '" ';
                    $output .= 'data-coupon-id="' . esc_attr($coupon->ID) . '" ';
                    $output .= 'data-store-id="' . esc_attr($store_id) . '" ';
                    $output .= 'data-store-logo="' . esc_url($store_thumbnail) . '" ';
                    $output .= 'data-store-name="' . esc_attr($store->post_title) . '" ';
                    $output .= 'data-coupon-title="' . esc_attr($coupon->post_title) . '">';
                    $output .= esc_html($button_text);

                    // Add icon if set
                    $print_icon = $settings['print_button_icon'];
                    if (!empty($print_icon['value'])) {
                        $output .= ' <span class="button-icon">';
                        if (is_array($print_icon) && !empty($print_icon['value'])) {
                            // For SVG icons
                            if (isset($print_icon['library']) && $print_icon['library'] === 'svg') {
                                $output .= $print_icon['value'];
                            } else {
                                // For Font Awesome icons
                                $output .= '<i class="' . esc_attr($print_icon['value']) . '"></i>';
                            }
                        }
                        $output .= '</span>';
                    } else {
                        // Default icon if none set
                        $output .= ' <i class="fas fa-arrow-right"></i>';
                    }
                    $output .= '</button>';
                }
                $output .= '</div>'; // End coupon-button-wrapper
            }
            $output .= '</div>'; // End store-coupon-layout
            $output .= '</div>'; // End store-coupon-item
            $output .= '</div>'; // End store-coupon-item-wrapper
        }
        $output .= '</div>'; // End store-coupons-container

        // Add pagination if enabled
        if($settings['enable_pagination'] === 'yes'){

            $total_pages = $coupons_query->max_num_pages;

            if($total_pages > 1){
                
                $output .= '<div class="coupon-pagination">';
    
                // Previous page
                if($current_page > 1){
                    $prev_link = add_query_arg('coupon_page', $current_page - 1);
                    if(!empty($current_filter)){
                        $prev_link = add_query_arg('filter', $current_filter, $prev_link);
                    }
                    $output .= '<a href="' . esc_url($prev_link) . '" class="pagination-item prev"><i class="fas fa-chevron-left"></i></a>';
                }
    
                // Page numbers
                $start_page = max(1, $current_page - 2);
                $end_page = min($total_pages, $current_page + 2);
    
                for($i = $start_page; $i <= $end_page; $i++){
                    $page_link = add_query_arg('coupon_page', $i);
                    if(!empty($current_filter)){
                        $page_link = add_query_arg('filter', $current_filter, $page_link);
                    }
    
                    $active_class = ($i === $current_page) ? 'active' : '';
                    $output .= '<a href="' . esc_url($page_link) . '" class="pagination-item ' . $active_class . '">' . $i . '</a>';
                }
    
                // Next page
                if($current_page < $total_pages){
                    $next_link = add_query_arg('coupon_page', $current_page + 1);
                    if(!empty($current_filter)){
                        $next_link = add_query_arg('filter', $current_filter, $next_link);
                    }
                    $output .= '<a href="' . esc_url($next_link) . '" class="pagination-item next"><i class="fas fa-chevron-right"></i></a>';
                }
                $output .= '</div>'; // End coupon-pagination
            }


        }
        // Popup template
        $output .= '<div id="coupon-popup-template" class="coupon-popup-template" style="display: none;">';
        $output .= '<div class="coupon-popup">';
        $output .= '<div class="popup-header">';
        $output .= '<h3 class="popup-title"></h3>';
        $output .= '<button class="popup-close">&times;</button>';
        $output .= '</div>';
        $output .= '<div class="popup-content">';
        $output .= '<div class="popup-store-logo">';
        $output .= '<img src="" alt="">';
        $output .= '</div>';
        $output .= '<div class="popup-code-container">';
        $output .= '<div class="popup-code"></div>';
        $output .= '<div class="popup-code-message">' . esc_html__('Click the code to auto copy', 'advance-coupon') . '</div>';
        $output .= '</div>';
        $output .= '<div class="popup-footer">';
        $output .= '<div class="popup-info-toggle">' . esc_html__('Show Information', 'advance-coupon') . '</div>';
        $output .= '<div class="popup-usage-count"></div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';
        $output .= '</div>';

        echo $output;

        // Enqueue required scripts and styles
        wp_enqueue_style('font-awesome');
        wp_enqueue_style('store-coupons-widget-style', ADVCOUPON_PLUGIN_URL . 'assets/css/store-coupons-widget.css', array(), ADVCOUPON_VERSION);
        wp_enqueue_script('store-coupons-widget-script', ADVCOUPON_PLUGIN_URL . 'assets/js/store-coupons-final.js', array('jquery'), ADVCOUPON_VERSION, true);

        // Localize script with AJAX data
        wp_localize_script('store-coupons-widget-script', 'advance_coupon_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('advance_coupon_ajax_nonce')
        ));

    }


    /**
     * Get store IDs array by category ID
     *
     * @return void
     */

     protected function get_store_info_by_category($category_id) {
         $stores = array();
         
         // Get stores associated with this category
         $args = array(
             'post_type' => 'store',
             'posts_per_page' => -1,
             'tax_query' => array(
                 array(
                     'taxonomy' => 'store_category',
                     'field' => 'term_id',
                     'terms' => $category_id,
                 ),
                 'field' => 'ids', // Only get IDs for efficiency
             ),
         );

         $store_query = new \WP_Query($args);

         if ($store_query->have_posts()) {
             while ($store_query->have_posts()) {
                 $store_query->the_post();
                 $store_id = get_the_ID();
                 
                 array_push($stores, $store_id);
             }
             wp_reset_postdata();
         }

         return $stores; // Return array of store IDs
     }


}

