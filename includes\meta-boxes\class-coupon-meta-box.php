<?php
/**
 * Coupon Meta Box
 */
class Coupon_Meta_Box {
    /**
     * Register meta boxes
     */
    public function register() {
        add_meta_box(
            'coupon_details',
            __('Coupon Details', 'advance-coupon'),
            array($this, 'render_coupon_details'),
            'coupon',
            'normal',
            'high'
        );

        // Enqueue Select2 for the meta box
        add_action('admin_enqueue_scripts', array($this, 'enqueue_scripts'));
    }

    /**
     * Enqueue scripts and styles for the meta box
     */
    public function enqueue_scripts($hook) {
        global $post_type;

        // Only enqueue on coupon add/edit screens
        if (('post.php' === $hook || 'post-new.php' === $hook) && 'coupon' === $post_type) {
            // Enqueue Select2 CSS
            wp_enqueue_style(
                'select2',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css',
                array(),
                '4.1.0-rc.0'
            );

            // Enqueue Select2 JS
            wp_enqueue_script(
                'select2',
                'https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js',
                array('jquery'),
                '4.1.0-rc.0',
                true
            );
        }
    }

    /**
     * Render coupon details meta box
     */
    public function render_coupon_details($post) {
        // Add nonce for security
        wp_nonce_field('coupon_details_nonce', 'coupon_details_nonce');

        // Get saved values
        $expire_time = get_post_meta($post->ID, '_expire_time', true);
        $coupon_type = get_post_meta($post->ID, '_coupon_type', true);
        $is_exclusive = get_post_meta($post->ID, '_is_exclusive', true);
        $is_verified = get_post_meta($post->ID, '_is_verified', true);
        $affiliate_link = get_post_meta($post->ID, '_affiliate_link', true);
        $coupon_link = get_post_meta($post->ID, '_coupon_link', true);
        $printable_image_id = get_post_meta($post->ID, '_printable_image', true);
        $specific_link = get_post_meta($post->ID, '_specific_link', true);
        $code = get_post_meta($post->ID, '_code', true);
        $used_count = get_post_meta($post->ID, '_used_count', true) ? get_post_meta($post->ID, '_used_count', true) : 0;
        $store_id = get_post_meta($post->ID, '_store_id', true);

        // Get all stores for dropdown (including drafts and other statuses)
        $stores = get_posts(array(
            'post_type' => 'store',
            'posts_per_page' => -1,
            'orderby' => 'title',
            'order' => 'ASC',
            'post_status' => array('publish', 'draft', 'pending', 'private'),
        ));

        // Output fields
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="store_id"><?php _e('Store', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <select id="store_id" name="store_id" class="regular-text select2-enabled">
                        <option value=""><?php _e('Select a store', 'advance-coupon'); ?></option>
                        <?php foreach ($stores as $store) : ?>
                            <option value="<?php echo esc_attr($store->ID); ?>" <?php selected($store_id, $store->ID); ?>>
                                <?php echo esc_html($store->post_title); ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <p class="description"><?php _e('Select the store this coupon belongs to', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="expire_time"><?php _e('Expire Time', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="datetime-local" id="expire_time" name="expire_time" value="<?php echo esc_attr($expire_time); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the expiration date and time for this coupon', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="coupon_type"><?php _e('Coupon Type', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <select id="coupon_type" name="coupon_type" class="regular-text">
                        <option value="online_sale" <?php selected($coupon_type, 'online_sale'); ?>><?php _e('Online Sale', 'advance-coupon'); ?></option>
                        <option value="online_code" <?php selected($coupon_type, 'online_code'); ?>><?php _e('Online Code', 'advance-coupon'); ?></option>
                        <option value="in_store_code" <?php selected($coupon_type, 'in_store_code'); ?>><?php _e('In Store Code', 'advance-coupon'); ?></option>
                    </select>
                    <p class="description"><?php _e('Select the type of coupon', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="is_exclusive"><?php _e('Is Exclusive', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="is_exclusive" name="is_exclusive" value="1" <?php checked($is_exclusive, '1'); ?>>
                    <p class="description"><?php _e('Check if this is an exclusive coupon', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="is_verified"><?php _e('Is Verified', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="checkbox" id="is_verified" name="is_verified" value="1" <?php checked($is_verified, '1'); ?>>
                    <p class="description"><?php _e('Check if this coupon has been verified', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="affiliate_link"><?php _e('Affiliate Link', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="url" id="affiliate_link" name="affiliate_link" value="<?php echo esc_url($affiliate_link); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the affiliate link for this coupon', 'advance-coupon'); ?></p>
                </td>
            </tr>

            <!-- Conditional fields based on coupon type -->
            <tr class="field-online-sale" style="display: <?php echo ($coupon_type === 'online_sale') ? 'table-row' : 'none'; ?>">
                <th scope="row">
                    <label for="coupon_link"><?php _e('Coupon Link', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="url" id="coupon_link" name="coupon_link" value="<?php echo esc_url($coupon_link); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the direct link to the coupon or deal', 'advance-coupon'); ?></p>
                </td>
            </tr>

            <tr class="field-in-store-code" style="display: <?php echo ($coupon_type === 'in_store_code') ? 'table-row' : 'none'; ?>">
                <th scope="row">
                    <label for="printable_image"><?php _e('Printable Image', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <div class="printable-image-container">
                        <div class="printable-image-preview">
                            <?php if ($printable_image_id) : ?>
                                <?php echo wp_get_attachment_image($printable_image_id, 'medium'); ?>
                            <?php endif; ?>
                        </div>
                        <input type="hidden" id="printable_image" name="printable_image" value="<?php echo esc_attr($printable_image_id); ?>">
                        <button type="button" class="button upload-printable-image"><?php _e('Upload Image', 'advance-coupon'); ?></button>
                        <button type="button" class="button remove-printable-image" <?php echo empty($printable_image_id) ? 'style="display:none;"' : ''; ?>><?php _e('Remove Image', 'advance-coupon'); ?></button>
                    </div>
                    <p class="description"><?php _e('Upload a printable image for in-store coupons', 'advance-coupon'); ?></p>
                </td>
            </tr>

            <tr class="field-online-code" style="display: <?php echo ($coupon_type === 'online_code') ? 'table-row' : 'none'; ?>">
                <th scope="row">
                    <label for="specific_link"><?php _e('Specific Link', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="url" id="specific_link" name="specific_link" value="<?php echo esc_url($specific_link); ?>" class="regular-text">
                    <p class="description"><?php _e('If coupon code must be applied to specific product/URL, add link here and it will overwrite link to store', 'advance-coupon'); ?></p>
                </td>
            </tr>

            <tr class="field-online-code" style="display: <?php echo ($coupon_type === 'online_code') ? 'table-row' : 'none'; ?>">
                <th scope="row">
                    <label for="code"><?php _e('Code', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="text" id="code" name="code" value="<?php echo esc_attr($code); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the coupon code', 'advance-coupon'); ?></p>
                </td>
            </tr>

            <tr>
                <th scope="row">
                    <label for="used_count"><?php _e('Used Count', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="number" id="used_count" name="used_count" value="<?php echo esc_attr($used_count); ?>" class="small-text" min="0">
                    <p class="description"><?php _e('Number of times this coupon has been used', 'advance-coupon'); ?></p>
                </td>
            </tr>
        </table>

        <script>
            jQuery(document).ready(function($) {
                // Initialize Select2 for store dropdown
                if ($.fn.select2) {
                    $('.select2-enabled').select2({
                        width: '100%',
                        placeholder: '<?php _e("Search for a store...", "advance-coupon"); ?>',
                        allowClear: true
                    });
                }

                // Handle coupon type change
                $('#coupon_type').on('change', function() {
                    var type = $(this).val();

                    // Hide all conditional fields
                    $('.field-online-sale, .field-online-code, .field-in-store-code').hide();

                    // Show fields based on selected type
                    if (type === 'online_sale') {
                        $('.field-online-sale').show();
                    } else if (type === 'online_code') {
                        $('.field-online-code').show();
                    } else if (type === 'in_store_code') {
                        $('.field-in-store-code').show();
                    }
                });

                // Handle printable image upload
                $('.upload-printable-image').on('click', function(e) {
                    e.preventDefault();

                    var button = $(this);
                    var container = button.closest('.printable-image-container');
                    var preview = container.find('.printable-image-preview');
                    var input = container.find('#printable_image');
                    var removeButton = container.find('.remove-printable-image');

                    var frame = wp.media({
                        title: '<?php _e('Select or Upload Printable Image', 'advance-coupon'); ?>',
                        button: {
                            text: '<?php _e('Use this image', 'advance-coupon'); ?>'
                        },
                        multiple: false
                    });

                    frame.on('select', function() {
                        var attachment = frame.state().get('selection').first().toJSON();
                        preview.html('<img src="' + attachment.url + '" alt="" style="max-width:100%;">');
                        input.val(attachment.id);
                        removeButton.show();
                    });

                    frame.open();
                });

                // Handle remove printable image
                $('.remove-printable-image').on('click', function(e) {
                    e.preventDefault();

                    var button = $(this);
                    var container = button.closest('.printable-image-container');
                    var preview = container.find('.printable-image-preview');
                    var input = container.find('#printable_image');

                    preview.empty();
                    input.val('');
                    button.hide();
                });
            });
        </script>
        <?php
    }

    /**
     * Save meta box data
     */
    public function save($post_id) {
        // Check if nonce is set
        if (!isset($_POST['coupon_details_nonce'])) {
            return;
        }

        // Verify nonce
        if (!wp_verify_nonce($_POST['coupon_details_nonce'], 'coupon_details_nonce')) {
            return;
        }

        // Check if autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save store ID
        if (isset($_POST['store_id']) && !empty($_POST['store_id'])) {
            $store_id = sanitize_text_field($_POST['store_id']);
            update_post_meta($post_id, '_store_id', $store_id);

            // Get store categories and assign them to this coupon
            $this->sync_store_categories_to_coupon($post_id, $store_id);
        }

        // Save expire time
        if (isset($_POST['expire_time'])) {
            update_post_meta($post_id, '_expire_time', sanitize_text_field($_POST['expire_time']));
        }

        // Save coupon type
        if (isset($_POST['coupon_type'])) {
            update_post_meta($post_id, '_coupon_type', sanitize_text_field($_POST['coupon_type']));
        }

        // Save is exclusive
        $is_exclusive = isset($_POST['is_exclusive']) ? '1' : '0';
        update_post_meta($post_id, '_is_exclusive', $is_exclusive);

        // Save is verified
        $is_verified = isset($_POST['is_verified']) ? '1' : '0';
        update_post_meta($post_id, '_is_verified', $is_verified);

        // Save affiliate link
        if (isset($_POST['affiliate_link'])) {
            update_post_meta($post_id, '_affiliate_link', esc_url_raw($_POST['affiliate_link']));
        }

        // Save coupon link (for online sale)
        if (isset($_POST['coupon_link'])) {
            update_post_meta($post_id, '_coupon_link', esc_url_raw($_POST['coupon_link']));
        }

        // Save printable image (for in-store code)
        if (isset($_POST['printable_image'])) {
            update_post_meta($post_id, '_printable_image', sanitize_text_field($_POST['printable_image']));
        }

        // Save specific link (for online code)
        if (isset($_POST['specific_link'])) {
            update_post_meta($post_id, '_specific_link', esc_url_raw($_POST['specific_link']));
        }

        // Save code (for online code)
        if (isset($_POST['code'])) {
            update_post_meta($post_id, '_code', sanitize_text_field($_POST['code']));
        }

        // Save used count
        if (isset($_POST['used_count'])) {
            update_post_meta($post_id, '_used_count', intval($_POST['used_count']));
        }
    }

    /**
     * Sync store categories to coupon
     *
     * @param int $coupon_id The coupon ID
     * @param int $store_id The store ID
     */
    private function sync_store_categories_to_coupon($coupon_id, $store_id) {
        // Get store categories
        $store_terms = wp_get_object_terms($store_id, 'store_category', array('fields' => 'ids'));

        if (!is_wp_error($store_terms) && !empty($store_terms)) {
            // Create a custom taxonomy term relationship for the coupon
            // We're using a custom meta field to store the relationship since we removed the taxonomy from coupons
            update_post_meta($coupon_id, '_store_categories', $store_terms);
        } else {
            // If store has no categories or there was an error, clear any existing categories
            delete_post_meta($coupon_id, '_store_categories');
        }
    }
}
