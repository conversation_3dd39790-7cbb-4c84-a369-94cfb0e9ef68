/**
 * Store Coupons Widget Styles
 *
 * This file contains all styles for the store coupons widget.
 * Following WordPress best practices for CSS organization.
 */

/* Main container */
.store-coupons-container {
    margin-bottom: 30px;
}

/* Coupon item wrapper */
.store-coupon-item-wrapper {
    margin-bottom: 20px;
    filter: drop-shadow(0 5px 3px rgba(0,0,0,0.1));
    transition: all 0.3s ease;
}

.store-coupon-item-wrapper:hover {
    filter: drop-shadow(1px 8px 4px rgba(0,0,0,0.15));
}

/* Coupon item */
.store-coupon-item {
    border-radius: 8px;
    overflow: hidden;
    background-color: #f8f9fa;
    position: relative;
}

/* Add padding for zigzag pattern to prevent button overlap */
.store-coupons-container.has-zigzag .store-coupon-item {
    padding-right: var(--zigzag-size, 20px);
}

/* CSS Variables for zigzag pattern */
.store-coupons-container {
    --zigzag-size: 20px;
    --zigzag-density: 9;
}

/* Zigzag pattern - right side only - dynamic generation based on density */
.store-coupons-container.has-zigzag.zigzag-right-side .store-coupon-item {
    position: relative;
}

/* Generate different zigzag patterns based on density */
.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-2 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 50%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-3 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 33.33%, calc(100% - var(--zigzag-size)) 66.66%,
        100% 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-4 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 25%, calc(100% - var(--zigzag-size)) 50%,
        100% 75%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-5 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 20%, calc(100% - var(--zigzag-size)) 40%,
        100% 60%, calc(100% - var(--zigzag-size)) 80%,
        100% 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-6 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 16.66%, calc(100% - var(--zigzag-size)) 33.33%,
        100% 50%, calc(100% - var(--zigzag-size)) 66.66%,
        100% 83.33%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-8 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 12.5%, calc(100% - var(--zigzag-size)) 25%,
        100% 37.5%, calc(100% - var(--zigzag-size)) 50%,
        100% 62.5%, calc(100% - var(--zigzag-size)) 75%,
        100% 87.5%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-10 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 10%, calc(100% - var(--zigzag-size)) 20%,
        100% 30%, calc(100% - var(--zigzag-size)) 40%,
        100% 50%, calc(100% - var(--zigzag-size)) 60%,
        100% 70%, calc(100% - var(--zigzag-size)) 80%,
        100% 90%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-12 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 8.33%, calc(100% - var(--zigzag-size)) 16.66%,
        100% 25%, calc(100% - var(--zigzag-size)) 33.33%,
        100% 41.66%, calc(100% - var(--zigzag-size)) 50%,
        100% 58.33%, calc(100% - var(--zigzag-size)) 66.66%,
        100% 75%, calc(100% - var(--zigzag-size)) 83.33%,
        100% 91.66%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-15 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 6.66%, calc(100% - var(--zigzag-size)) 13.33%,
        100% 20%, calc(100% - var(--zigzag-size)) 26.66%,
        100% 33.33%, calc(100% - var(--zigzag-size)) 40%,
        100% 46.66%, calc(100% - var(--zigzag-size)) 53.33%,
        100% 60%, calc(100% - var(--zigzag-size)) 66.66%,
        100% 73.33%, calc(100% - var(--zigzag-size)) 80%,
        100% 86.66%, calc(100% - var(--zigzag-size)) 93.33%,
        100% 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-20 .store-coupon-item {
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% 5%, calc(100% - var(--zigzag-size)) 10%,
        100% 15%, calc(100% - var(--zigzag-size)) 20%,
        100% 25%, calc(100% - var(--zigzag-size)) 30%,
        100% 35%, calc(100% - var(--zigzag-size)) 40%,
        100% 45%, calc(100% - var(--zigzag-size)) 50%,
        100% 55%, calc(100% - var(--zigzag-size)) 60%,
        100% 65%, calc(100% - var(--zigzag-size)) 70%,
        100% 75%, calc(100% - var(--zigzag-size)) 80%,
        100% 85%, calc(100% - var(--zigzag-size)) 90%,
        100% 95%, calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-25 .store-coupon-item {
    --step: 4%; /* 100% / 25 */
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% var(--step), calc(100% - var(--zigzag-size)) calc(var(--step) * 2),
        100% calc(var(--step) * 3), calc(100% - var(--zigzag-size)) calc(var(--step) * 4),
        100% calc(var(--step) * 5), calc(100% - var(--zigzag-size)) calc(var(--step) * 6),
        100% calc(var(--step) * 7), calc(100% - var(--zigzag-size)) calc(var(--step) * 8),
        100% calc(var(--step) * 9), calc(100% - var(--zigzag-size)) calc(var(--step) * 10),
        100% calc(var(--step) * 11), calc(100% - var(--zigzag-size)) calc(var(--step) * 12),
        100% calc(var(--step) * 13), calc(100% - var(--zigzag-size)) calc(var(--step) * 14),
        100% calc(var(--step) * 15), calc(100% - var(--zigzag-size)) calc(var(--step) * 16),
        100% calc(var(--step) * 17), calc(100% - var(--zigzag-size)) calc(var(--step) * 18),
        100% calc(var(--step) * 19), calc(100% - var(--zigzag-size)) calc(var(--step) * 20),
        100% calc(var(--step) * 21), calc(100% - var(--zigzag-size)) calc(var(--step) * 22),
        100% calc(var(--step) * 23), calc(100% - var(--zigzag-size)) calc(var(--step) * 24),
        100% calc(var(--step) * 25), 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-30 .store-coupon-item {
    --step: 3.33%; /* 100% / 30 */
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% var(--step), calc(100% - var(--zigzag-size)) calc(var(--step) * 2),
        100% calc(var(--step) * 3), calc(100% - var(--zigzag-size)) calc(var(--step) * 4),
        100% calc(var(--step) * 5), calc(100% - var(--zigzag-size)) calc(var(--step) * 6),
        100% calc(var(--step) * 7), calc(100% - var(--zigzag-size)) calc(var(--step) * 8),
        100% calc(var(--step) * 9), calc(100% - var(--zigzag-size)) calc(var(--step) * 10),
        100% calc(var(--step) * 11), calc(100% - var(--zigzag-size)) calc(var(--step) * 12),
        100% calc(var(--step) * 13), calc(100% - var(--zigzag-size)) calc(var(--step) * 14),
        100% calc(var(--step) * 15), calc(100% - var(--zigzag-size)) calc(var(--step) * 16),
        100% calc(var(--step) * 17), calc(100% - var(--zigzag-size)) calc(var(--step) * 18),
        100% calc(var(--step) * 19), calc(100% - var(--zigzag-size)) calc(var(--step) * 20),
        100% calc(var(--step) * 21), calc(100% - var(--zigzag-size)) calc(var(--step) * 22),
        100% calc(var(--step) * 23), calc(100% - var(--zigzag-size)) calc(var(--step) * 24),
        100% calc(var(--step) * 25), calc(100% - var(--zigzag-size)) calc(var(--step) * 26),
        100% calc(var(--step) * 27), calc(100% - var(--zigzag-size)) calc(var(--step) * 28),
        100% calc(var(--step) * 29), calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

.store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-40 .store-coupon-item {
    --step: 2.5%; /* 100% / 40 */
    clip-path: polygon(
        0% 0%, calc(100% - var(--zigzag-size)) 0%,
        100% var(--step), calc(100% - var(--zigzag-size)) calc(var(--step) * 2),
        100% calc(var(--step) * 3), calc(100% - var(--zigzag-size)) calc(var(--step) * 4),
        100% calc(var(--step) * 5), calc(100% - var(--zigzag-size)) calc(var(--step) * 6),
        100% calc(var(--step) * 7), calc(100% - var(--zigzag-size)) calc(var(--step) * 8),
        100% calc(var(--step) * 9), calc(100% - var(--zigzag-size)) calc(var(--step) * 10),
        100% calc(var(--step) * 11), calc(100% - var(--zigzag-size)) calc(var(--step) * 12),
        100% calc(var(--step) * 13), calc(100% - var(--zigzag-size)) calc(var(--step) * 14),
        100% calc(var(--step) * 15), calc(100% - var(--zigzag-size)) calc(var(--step) * 16),
        100% calc(var(--step) * 17), calc(100% - var(--zigzag-size)) calc(var(--step) * 18),
        100% calc(var(--step) * 19), calc(100% - var(--zigzag-size)) calc(var(--step) * 20),
        100% calc(var(--step) * 21), calc(100% - var(--zigzag-size)) calc(var(--step) * 22),
        100% calc(var(--step) * 23), calc(100% - var(--zigzag-size)) calc(var(--step) * 24),
        100% calc(var(--step) * 25), calc(100% - var(--zigzag-size)) calc(var(--step) * 26),
        100% calc(var(--step) * 27), calc(100% - var(--zigzag-size)) calc(var(--step) * 28),
        100% calc(var(--step) * 29), calc(100% - var(--zigzag-size)) calc(var(--step) * 30),
        100% calc(var(--step) * 31), calc(100% - var(--zigzag-size)) calc(var(--step) * 32),
        100% calc(var(--step) * 33), calc(100% - var(--zigzag-size)) calc(var(--step) * 34),
        100% calc(var(--step) * 35), calc(100% - var(--zigzag-size)) calc(var(--step) * 36),
        100% calc(var(--step) * 37), calc(100% - var(--zigzag-size)) calc(var(--step) * 38),
        100% calc(var(--step) * 39), calc(100% - var(--zigzag-size)) 100%, 0% 100%
    );
}

/* Zigzag pattern - all sides */
.store-coupons-container.has-zigzag.zigzag-all-sides .store-coupon-item {
    position: relative;
}

/* All sides zigzag with density 5 (medium) */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-5 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        20% 0%, 40% var(--zigzag-size), 60% 0%, 80% var(--zigzag-size),
        100% 0%,

        /* Right edge */
        100% 20%, calc(100% - var(--zigzag-size)) 40%, 100% 60%, calc(100% - var(--zigzag-size)) 80%,
        100% 100%,

        /* Bottom edge */
        80% 100%, 60% calc(100% - var(--zigzag-size)), 40% 100%, 20% calc(100% - var(--zigzag-size)),
        0% 100%,

        /* Left edge */
        0% 80%, var(--zigzag-size) 60%, 0% 40%, var(--zigzag-size) 20%
    );
}

/* All sides zigzag with density 4 (fewer points) */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-4 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        25% 0%, 50% var(--zigzag-size), 75% 0%,
        100% var(--zigzag-size),

        /* Right edge */
        100% 25%, calc(100% - var(--zigzag-size)) 50%, 100% 75%,
        calc(100% - var(--zigzag-size)) 100%,

        /* Bottom edge */
        75% 100%, 50% calc(100% - var(--zigzag-size)), 25% 100%,
        0% calc(100% - var(--zigzag-size)),

        /* Left edge */
        0% 75%, var(--zigzag-size) 50%, 0% 25%
    );
}

/* All sides zigzag with density 3 (few points) */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-3 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        33.33% 0%, 66.66% var(--zigzag-size),
        100% 0%,

        /* Right edge */
        100% 33.33%, calc(100% - var(--zigzag-size)) 66.66%,
        100% 100%,

        /* Bottom edge */
        66.66% 100%, 33.33% calc(100% - var(--zigzag-size)),
        0% 100%,

        /* Left edge */
        0% 66.66%, var(--zigzag-size) 33.33%
    );
}

/* All sides zigzag with density 2 (minimal points) */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-2 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        50% 0%,
        100% var(--zigzag-size),

        /* Right edge */
        100% 50%,
        calc(100% - var(--zigzag-size)) 100%,

        /* Bottom edge */
        50% 100%,
        0% calc(100% - var(--zigzag-size)),

        /* Left edge */
        0% 50%
    );
}

/* All sides zigzag with density 6 */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-6 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        16.66% 0%, 33.33% var(--zigzag-size), 50% 0%, 66.66% var(--zigzag-size), 83.33% 0%,
        100% var(--zigzag-size),

        /* Right edge */
        100% 16.66%, calc(100% - var(--zigzag-size)) 33.33%, 100% 50%, calc(100% - var(--zigzag-size)) 66.66%, 100% 83.33%,
        calc(100% - var(--zigzag-size)) 100%,

        /* Bottom edge */
        83.33% 100%, 66.66% calc(100% - var(--zigzag-size)), 50% 100%, 33.33% calc(100% - var(--zigzag-size)), 16.66% 100%,
        0% calc(100% - var(--zigzag-size)),

        /* Left edge */
        0% 83.33%, var(--zigzag-size) 66.66%, 0% 50%, var(--zigzag-size) 33.33%, 0% 16.66%
    );
}

/* All sides zigzag with density 8 */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-8 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        12.5% 0%, 25% var(--zigzag-size), 37.5% 0%, 50% var(--zigzag-size), 62.5% 0%, 75% var(--zigzag-size), 87.5% 0%,
        100% var(--zigzag-size),

        /* Right edge */
        100% 12.5%, calc(100% - var(--zigzag-size)) 25%, 100% 37.5%, calc(100% - var(--zigzag-size)) 50%,
        100% 62.5%, calc(100% - var(--zigzag-size)) 75%, 100% 87.5%,
        calc(100% - var(--zigzag-size)) 100%,

        /* Bottom edge */
        87.5% 100%, 75% calc(100% - var(--zigzag-size)), 62.5% 100%, 50% calc(100% - var(--zigzag-size)),
        37.5% 100%, 25% calc(100% - var(--zigzag-size)), 12.5% 100%,
        0% calc(100% - var(--zigzag-size)),

        /* Left edge */
        0% 87.5%, var(--zigzag-size) 75%, 0% 62.5%, var(--zigzag-size) 50%,
        0% 37.5%, var(--zigzag-size) 25%, 0% 12.5%
    );
}

/* All sides zigzag with density 10 */
.store-coupons-container.has-zigzag.zigzag-all-sides.zigzag-density-10 .store-coupon-item {
    clip-path: polygon(
        /* Top edge */
        0% var(--zigzag-size),
        10% 0%, 20% var(--zigzag-size), 30% 0%, 40% var(--zigzag-size), 50% 0%, 60% var(--zigzag-size), 70% 0%, 80% var(--zigzag-size), 90% 0%,
        100% var(--zigzag-size),

        /* Right edge */
        100% 10%, calc(100% - var(--zigzag-size)) 20%, 100% 30%, calc(100% - var(--zigzag-size)) 40%,
        100% 50%, calc(100% - var(--zigzag-size)) 60%, 100% 70%, calc(100% - var(--zigzag-size)) 80%, 100% 90%,
        calc(100% - var(--zigzag-size)) 100%,

        /* Bottom edge */
        90% 100%, 80% calc(100% - var(--zigzag-size)), 70% 100%, 60% calc(100% - var(--zigzag-size)),
        50% 100%, 40% calc(100% - var(--zigzag-size)), 30% 100%, 20% calc(100% - var(--zigzag-size)), 10% 100%,
        0% calc(100% - var(--zigzag-size)),

        /* Left edge */
        0% 90%, var(--zigzag-size) 80%, 0% 70%, var(--zigzag-size) 60%,
        0% 50%, var(--zigzag-size) 40%, 0% 30%, var(--zigzag-size) 20%, 0% 10%
    );
}

/* Mobile styles for zigzag pattern */
@media (max-width: 767px) {
    /* Reset any desktop-specific zigzag patterns */
    .store-coupons-container.has-zigzag.zigzag-right-side .store-coupon-item {
        /* Reset right-side only pattern */
        clip-path: none;
    }

    /* Apply all-sides zigzag patterns to all items on mobile */
    /* Use higher specificity to override desktop styles */
    .store-coupons-container.has-zigzag.zigzag-density-5 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-5 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            20% 0%, 40% var(--zigzag-size), 60% 0%, 80% var(--zigzag-size),
            100% 0%,

            /* Right edge */
            100% 20%, calc(100% - var(--zigzag-size)) 40%, 100% 60%, calc(100% - var(--zigzag-size)) 80%,
            100% 100%,

            /* Bottom edge */
            80% 100%, 60% calc(100% - var(--zigzag-size)), 40% 100%, 20% calc(100% - var(--zigzag-size)),
            0% 100%,

            /* Left edge */
            0% 80%, var(--zigzag-size) 60%, 0% 40%, var(--zigzag-size) 20%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-4 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-4 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            25% 0%, 50% var(--zigzag-size), 75% 0%,
            100% var(--zigzag-size),

            /* Right edge */
            100% 25%, calc(100% - var(--zigzag-size)) 50%, 100% 75%,
            calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge */
            75% 100%, 50% calc(100% - var(--zigzag-size)), 25% 100%,
            0% calc(100% - var(--zigzag-size)),

            /* Left edge */
            0% 75%, var(--zigzag-size) 50%, 0% 25%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-3 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-3 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            33.33% 0%, 66.66% var(--zigzag-size),
            100% 0%,

            /* Right edge */
            100% 33.33%, calc(100% - var(--zigzag-size)) 66.66%,
            100% 100%,

            /* Bottom edge */
            66.66% 100%, 33.33% calc(100% - var(--zigzag-size)),
            0% 100%,

            /* Left edge */
            0% 66.66%, var(--zigzag-size) 33.33%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-2 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-2 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            50% 0%,
            100% var(--zigzag-size),

            /* Right edge */
            100% 50%,
            calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge */
            50% 100%,
            0% calc(100% - var(--zigzag-size)),

            /* Left edge */
            0% 50%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-6 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-6 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            16.66% 0%, 33.33% var(--zigzag-size), 50% 0%, 66.66% var(--zigzag-size), 83.33% 0%,
            100% var(--zigzag-size),

            /* Right edge */
            100% 16.66%, calc(100% - var(--zigzag-size)) 33.33%, 100% 50%, calc(100% - var(--zigzag-size)) 66.66%, 100% 83.33%,
            calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge */
            83.33% 100%, 66.66% calc(100% - var(--zigzag-size)), 50% 100%, 33.33% calc(100% - var(--zigzag-size)), 16.66% 100%,
            0% calc(100% - var(--zigzag-size)),

            /* Left edge */
            0% 83.33%, var(--zigzag-size) 66.66%, 0% 50%, var(--zigzag-size) 33.33%, 0% 16.66%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-8 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-8 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            12.5% 0%, 25% var(--zigzag-size), 37.5% 0%, 50% var(--zigzag-size), 62.5% 0%, 75% var(--zigzag-size), 87.5% 0%,
            100% var(--zigzag-size),

            /* Right edge */
            100% 12.5%, calc(100% - var(--zigzag-size)) 25%, 100% 37.5%, calc(100% - var(--zigzag-size)) 50%,
            100% 62.5%, calc(100% - var(--zigzag-size)) 75%, 100% 87.5%,
            calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge */
            87.5% 100%, 75% calc(100% - var(--zigzag-size)), 62.5% 100%, 50% calc(100% - var(--zigzag-size)),
            37.5% 100%, 25% calc(100% - var(--zigzag-size)), 12.5% 100%,
            0% calc(100% - var(--zigzag-size)),

            /* Left edge */
            0% 87.5%, var(--zigzag-size) 75%, 0% 62.5%, var(--zigzag-size) 50%,
            0% 37.5%, var(--zigzag-size) 25%, 0% 12.5%
        );
    }

    .store-coupons-container.has-zigzag.zigzag-density-10 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-10 .store-coupon-item {
        clip-path: polygon(
            /* Top edge */
            0% var(--zigzag-size),
            10% 0%, 20% var(--zigzag-size), 30% 0%, 40% var(--zigzag-size), 50% 0%, 60% var(--zigzag-size), 70% 0%, 80% var(--zigzag-size), 90% 0%,
            100% var(--zigzag-size),

            /* Right edge */
            100% 10%, calc(100% - var(--zigzag-size)) 20%, 100% 30%, calc(100% - var(--zigzag-size)) 40%,
            100% 50%, calc(100% - var(--zigzag-size)) 60%, 100% 70%, calc(100% - var(--zigzag-size)) 80%, 100% 90%,
            calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge */
            90% 100%, 80% calc(100% - var(--zigzag-size)), 70% 100%, 60% calc(100% - var(--zigzag-size)),
            50% 100%, 40% calc(100% - var(--zigzag-size)), 30% 100%, 20% calc(100% - var(--zigzag-size)), 10% 100%,
            0% calc(100% - var(--zigzag-size)),

            /* Left edge */
            0% 90%, var(--zigzag-size) 80%, 0% 70%, var(--zigzag-size) 60%,
            0% 50%, var(--zigzag-size) 40%, 0% 30%, var(--zigzag-size) 20%, 0% 10%
        );
    }

    /* Higher density zigzags (12+) */
    .store-coupons-container.has-zigzag.zigzag-density-12 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-12 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-density-15 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-15 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-density-20 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-20 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-density-25 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-25 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-density-30 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-30 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-density-40 .store-coupon-item,
    .store-coupons-container.has-zigzag.zigzag-right-side.zigzag-density-40 .store-coupon-item {
        /* For mobile, we'll use a simplified version of the zigzag pattern for higher densities */
        /* This ensures better performance on mobile devices */
        --mobile-step: 10%;
        clip-path: polygon(
            /* Top edge - simplified to 10 points */
            0% var(--zigzag-size),
            var(--mobile-step) 0%, calc(var(--mobile-step) * 2) var(--zigzag-size),
            calc(var(--mobile-step) * 3) 0%, calc(var(--mobile-step) * 4) var(--zigzag-size),
            calc(var(--mobile-step) * 5) 0%, calc(var(--mobile-step) * 6) var(--zigzag-size),
            calc(var(--mobile-step) * 7) 0%, calc(var(--mobile-step) * 8) var(--zigzag-size),
            calc(var(--mobile-step) * 9) 0%, 100% var(--zigzag-size),

            /* Right edge - simplified to 10 points */
            100% var(--mobile-step), calc(100% - var(--zigzag-size)) calc(var(--mobile-step) * 2),
            100% calc(var(--mobile-step) * 3), calc(100% - var(--zigzag-size)) calc(var(--mobile-step) * 4),
            100% calc(var(--mobile-step) * 5), calc(100% - var(--zigzag-size)) calc(var(--mobile-step) * 6),
            100% calc(var(--mobile-step) * 7), calc(100% - var(--zigzag-size)) calc(var(--mobile-step) * 8),
            100% calc(var(--mobile-step) * 9), calc(100% - var(--zigzag-size)) 100%,

            /* Bottom edge - simplified to 10 points */
            calc(var(--mobile-step) * 9) 100%, calc(var(--mobile-step) * 8) calc(100% - var(--zigzag-size)),
            calc(var(--mobile-step) * 7) 100%, calc(var(--mobile-step) * 6) calc(100% - var(--zigzag-size)),
            calc(var(--mobile-step) * 5) 100%, calc(var(--mobile-step) * 4) calc(100% - var(--zigzag-size)),
            calc(var(--mobile-step) * 3) 100%, calc(var(--mobile-step) * 2) calc(100% - var(--zigzag-size)),
            var(--mobile-step) 100%, 0% calc(100% - var(--zigzag-size)),

            /* Left edge - simplified to 10 points */
            0% calc(var(--mobile-step) * 9), var(--zigzag-size) calc(var(--mobile-step) * 8),
            0% calc(var(--mobile-step) * 7), var(--zigzag-size) calc(var(--mobile-step) * 6),
            0% calc(var(--mobile-step) * 5), var(--zigzag-size) calc(var(--mobile-step) * 4),
            0% calc(var(--mobile-step) * 3), var(--zigzag-size) calc(var(--mobile-step) * 2),
            0% var(--mobile-step)
        );
    }
}

/* Desktop layout */
.store-coupon-layout {
    display: flex;
    align-items: stretch;
}

/* Store logo */
.store-logo {
    flex: 0 0 200px;
    min-width: 200px;
    max-width: 200px;
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    padding: 15px;
    border-right: 1px solid #eee;
}

.store-logo img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
    border-radius: 5px;
}

/* Coupon content */
.coupon-content {
    flex: 1;
    padding: 20px;
    display: flex;
    flex-direction: column;
}

/* Badges */
.coupon-badges {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    flex-wrap: wrap;
}

.coupon-badge {
    display: inline-flex;
    align-items: center;
    padding: 5px 12px;
    border-radius: 50px;
    font-size: 13px;
    background-color: #e9ecef;
    color: #495057;
}

.coupon-badge i {
    margin-right: 5px;
}

.coupon-badge.exclusive {
    background-color: #e3f2fd;
    color: #1976d2;
}

.coupon-badge.verified {
    background-color: #e0f7fa;
    color: #0097a7;
}

.coupon-badge.expiry {
    background-color: #e8f5e9;
    color: #388e3c;
}

.coupon-badge.expiry.expired {
    background-color: #ffebee;
    color: #d32f2f;
}

/* Coupon title */
.coupon-title {
    font-size: 20px;
    font-weight: 600;
    margin: 0 0 15px;
    color: #343a40;
    line-height: 1.3;
}

/* Coupon meta */
.coupon-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: #6c757d;
    margin-top: auto;
    padding-top: 15px;
}

.coupon-id-wrapper {
    display: flex;
    align-items: center;
}

.coupon-id-wrapper i {
    margin-right: 5px;
    color: #adb5bd;
}

.coupon-actions {
    display: flex;
    align-items: center;
    gap: 15px;
}

.coupon-views {
    display: flex;
    align-items: center;
}

.coupon-views:before {
    content: "\f06e";
    font-family: "FontAwesome";
    margin-right: 5px;
    color: #adb5bd;
}

.coupon-share {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.coupon-share:before {
    content: "\f064";
    font-family: "FontAwesome";
    margin-right: 5px;
    color: #adb5bd;
}

/* Button wrapper */
.coupon-button-wrapper {
    padding: 20px;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    position: relative;
    z-index: 2; /* Ensure buttons appear above the zigzag pattern */
}

/* CSS Variables for button styling */
:root {
    /* Common button properties */
    --button-padding: 12px 25px;
    --button-font-weight: 600;
    --button-font-size: 14px;
    --button-border-radius: 50px;
    --button-transition: all 0.3s ease;
    --button-letter-spacing: 0.5px;

    /* Get Deal button properties */
    --deal-button-bg: #4dabf7;
    --deal-button-color: #fff;
    --deal-button-hover-bg: #339af0;
    --deal-button-shadow: 0 2px 4px rgba(0,0,0,0.1);
    --deal-button-hover-shadow: 0 4px 8px rgba(0,0,0,0.1);

    /* Print Code button properties */
    --print-button-bg: transparent;
    --print-button-color: #4dabf7;
    --print-button-border: 1px dashed #4dabf7;
    --print-button-hover-bg: rgba(77, 171, 247, 0.1);
    --print-button-hover-shadow: 0 4px 8px rgba(0,0,0,0.05);

    /* Get Code button properties */
    --code-button-bg: #5B9EFF;
    --code-button-color: #fff;
    --code-button-hover-bg: #4a8cff;
    --code-button-shadow: 0 2px 4px rgba(0,0,0,0.1);

    /* Code preview properties */
    --code-preview-width: 80%;
    --code-preview-border: 2px dashed #5B9EFF;
    --code-preview-color: #5B9EFF;
    --code-preview-bg: transparent;
    --code-preview-font-family: monospace;
    --code-preview-font-weight: bold;
    --code-preview-font-size: 14px;
    --code-preview-padding-right: 20px;
}

/* Button styles */
.coupon-button {
    display: inline-block;
    padding: var(--button-padding);
    font-weight: var(--button-font-weight);
    font-size: var(--button-font-size);
    cursor: pointer;
    transition: var(--button-transition);
    text-transform: uppercase;
    letter-spacing: var(--button-letter-spacing);
    border-radius: var(--button-border-radius);
}

/* Deal button (Get Deal) - solid style with arrow */
.deal-button {
    background-color: var(--deal-button-bg);
    color: var(--deal-button-color);
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-shadow: var(--deal-button-shadow);
    margin-right: 5px; /* Add margin to prevent overlap with zigzag pattern */
}

.deal-button:hover {
    background-color: var(--deal-button-hover-bg);
    transform: translateY(-2px);
    box-shadow: var(--deal-button-hover-shadow);
}

.deal-button i {
    margin-left: 8px;
    font-size: 12px;
}

/* Print button (Print Code) - outlined style with arrow */
.print-button {
    background-color: var(--print-button-bg);
    color: var(--print-button-color);
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-right: 5px; /* Add margin to prevent overlap with zigzag pattern */
}

.print-button:hover {
    background-color: var(--print-button-hover-bg);
    transform: translateY(-2px);
    box-shadow: var(--print-button-hover-shadow);
}

.print-button i {
    margin-left: 8px;
    font-size: 12px;
}

/* Code button container and preview - folded ribbon effect */
.code-button-container {
    position: relative;
    display: inline-block;
    max-width: fit-content;
    transition: var(--button-transition);
    margin-right: 5px; /* Add margin to prevent overlap with zigzag pattern */
    overflow: hidden;
    border: none;
    outline: none;
    min-width: 120px;
}

.code-button-container:hover {
    transform: translateY(-2px);
}

/* Common styles for button and code preview */
.code-button,
.code-preview {
    display: inline-block;
    padding: var(--button-padding);
    font-weight: var(--button-font-weight);
    font-size: var(--button-font-size);
    text-transform: uppercase;
    box-sizing: border-box;
    line-height: 1.4;
    white-space: nowrap;
}

/* Main button styling for Get Code button */
.coupon-btn {
    position: relative;
    z-index: 1;
    width: auto;
    height: auto;
    clip-path: polygon(69% 1%, 100% 80%, 100% 100%, 0 100%, 0 0);
    border: none;
    background-color: var(--code-button-bg);
    color: var(--code-button-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
    display: inline-block;
    min-width: 120px;
}

.coupon-btn::after {
    position: absolute;
    top: 0;
    right: 0;
    width: 0;
    height: 0;
    border-width: 20px;
    border-style: solid;
    border-color: transparent transparent rgba(255,255,255, 0.3) rgba(255,255,255, 0.3);
    box-shadow: 1px 1px 5px rgba(0,0,0, 0.4);
    content: "";
}

.coupon-btn:hover {
    background-color: var(--code-button-hover-bg);
}

/* Regular code button styling (non-Get Code buttons) */
.code-button:not(.coupon-btn) {
    background-color: var(--code-button-bg);
    color: var(--code-button-color);
    border: none;
    position: relative;
    z-index: 2;
    cursor: pointer;
    transition: background-color 0.3s ease;
    box-shadow: var(--code-button-shadow);
    border-radius: var(--button-border-radius);
}

.code-button:not(.coupon-btn):hover {
    background-color: var(--code-button-hover-bg);
}

/* Code preview styling */
.code-preview {
    position: absolute;
    display: inline-flex;
    left: 0px;
    width: 100%;
    height: 100%;
    justify-content: flex-end;
    align-items: center;
    padding: 0px 5px;
    color: var(--code-preview-color);
    background-color: var(--code-preview-bg);
    z-index: 0;
    padding: 0;
    font-family: var(--code-preview-font-family);
    font-weight: var(--code-preview-font-weight);
    font-size: var(--code-preview-font-size);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
}

/* Popup Styles */
.coupon-popup-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: block;
    overflow-y: auto;
    text-align: center;
    z-index: 9999;
    padding: 30px 0;
}

.coupon-popup-overlay:before {
    content: '';
    display: inline-block;
    height: 100%;
    vertical-align: middle;
    margin-right: -0.25em; /* Adjusts for spacing */
}

.coupon-popup {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    width: 90%;
    max-width: 500px; /* Increased from 650px to 750px */
    position: relative;
    overflow: hidden;
    display: inline-block;
    vertical-align: middle;
    text-align: left;
    margin: 30px auto;
}

.popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #e9ecef;
    background-color: #f8f9fa;
}

.popup-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #343a40;
}

.popup-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    line-height: 1;
    padding: 0;
}

.popup-content {
    padding: 30px 40px;
}

.popup-store-logo {
    text-align: center;
    margin-bottom: 25px;
}

.popup-store-logo img {
    max-width: 150px;
    max-height: 150px;
    object-fit: contain;
}

.popup-code-container {
    margin-bottom: 25px;
}

.popup-code {
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    padding: 20px;
    text-align: center;
    font-weight: 600;
    font-size: 22px;
    margin-bottom: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    letter-spacing: 1px;
    user-select: all;
    border-radius: 6px;
}

.popup-code:hover {
    background-color: #e9ecef;
}

.popup-code.copied {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.popup-code-message {
    text-align: center;
    font-size: 12px;
    color: #6c757d;
}

.popup-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    font-size: 14px;
    color: #6c757d;
}

.popup-info-toggle {
    cursor: pointer;
    color: #4dabf7;
}

.popup-usage-count {
    display: flex;
    align-items: center;
}

.popup-usage-count:before {
    content: "\f007";
    font-family: "FontAwesome";
    margin-right: 5px;
    color: #adb5bd;
}

/* Mobile Styles */
@media (max-width: 767px) {
    .store-coupon-layout {
        flex-direction: column;
    }

    .store-logo {
        flex: 0 0 auto;
        width: 100%;
        min-width: auto;
        max-width: 100%;
        height: 180px;
        border-right: none;
        border-bottom: 1px solid #eee;
    }

    .coupon-content {
        width: 100%;
    }

    .coupon-button-wrapper {
        width: 100%;
        justify-content: center;
        padding-top: 5px;
    }

    .coupon-popup {
        width: 95%;
        max-width: none;
        margin: 10px;
    }

    .popup-store-logo img {
        max-height: 120px;
    }
}

/* Prevent body scroll when popup is open */
body.popup-open {
    overflow: hidden;
}

/* Filters */
.coupon-filters {
    margin-bottom: 20px;
}

.filter-list {
    display: flex;
    flex-wrap: wrap;
    list-style: none;
    padding: 0;
    margin: 0;
}

.filter-list li {
    margin: 0 10px 10px 0;
}

.filter-item {
    display: inline-block;
    padding: 8px 15px;
    background-color: #f8f9fa;
    color: #495057;
    border-radius: 30px;
    text-decoration: none;
    font-size: 14px;
    transition: all 0.3s ease;
}

.filter-item:hover {
    background-color: #e9ecef;
    color: #212529;
}

.filter-item.active {
    background-color: #4dabf7;
    color: #fff;
}

/* Pagination */
.coupon-pagination {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.pagination-item {
    display: inline-block;
    padding: 8px 15px;
    margin: 0 5px;
    background-color: #f8f9fa;
    color: #495057;
    border-radius: 4px;
    text-decoration: none;
    transition: all 0.3s ease;
}

.pagination-item:hover {
    background-color: #e9ecef;
    color: #212529;
}

.pagination-item.active {
    background-color: #4dabf7;
    color: #fff;
}

.pagination-item.prev,
.pagination-item.next {
    background-color: transparent;
    color: #4dabf7;
}

.pagination-item.prev:hover,
.pagination-item.next:hover {
    background-color: transparent;
    color: #339af0;
}
