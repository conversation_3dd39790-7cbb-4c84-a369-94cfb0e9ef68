<?php
/**
 * Store Post Type
 */
class Store_Post_Type {
    /**
     * Constructor
     */
    public function __construct() {
        // Register custom columns for the admin list table
        add_filter('manage_store_posts_columns', array($this, 'set_custom_columns'));
        add_action('manage_store_posts_custom_column', array($this, 'render_custom_columns'), 10, 2);

        // Set custom column order
        add_filter('manage_store_posts_columns', array($this, 'reorder_columns'));

        // Make columns sortable
        add_filter('manage_edit-store_sortable_columns', array($this, 'sortable_columns'));

        // Register taxonomy relationship after init
        add_action('init', array($this, 'register_taxonomy_relationship'), 15);
    }

    /**
     * Register the post type
     */
    public function register() {
        $labels = array(
            'name'                  => _x('Stores', 'Post type general name', 'advance-coupon'),
            'singular_name'         => _x('Store', 'Post type singular name', 'advance-coupon'),
            'menu_name'             => _x('Stores', 'Admin Menu text', 'advance-coupon'),
            'name_admin_bar'        => _x('Store', 'Add New on Toolbar', 'advance-coupon'),
            'add_new'               => __('Add New', 'advance-coupon'),
            'add_new_item'          => __('Add New Store', 'advance-coupon'),
            'new_item'              => __('New Store', 'advance-coupon'),
            'edit_item'             => __('Edit Store', 'advance-coupon'),
            'view_item'             => __('View Store', 'advance-coupon'),
            'all_items'             => __('All Stores', 'advance-coupon'),
            'search_items'          => __('Search Stores', 'advance-coupon'),
            'parent_item_colon'     => __('Parent Stores:', 'advance-coupon'),
            'not_found'             => __('No stores found.', 'advance-coupon'),
            'not_found_in_trash'    => __('No stores found in Trash.', 'advance-coupon'),
            'featured_image'        => _x('Store Image', 'Overrides the "Featured Image" phrase', 'advance-coupon'),
            'set_featured_image'    => _x('Set store image', 'Overrides the "Set featured image" phrase', 'advance-coupon'),
            'remove_featured_image' => _x('Remove store image', 'Overrides the "Remove featured image" phrase', 'advance-coupon'),
            'use_featured_image'    => _x('Use as store image', 'Overrides the "Use as featured image" phrase', 'advance-coupon'),
            'archives'              => _x('Store archives', 'The post type archive label used in nav menus', 'advance-coupon'),
            'insert_into_item'      => _x('Insert into store', 'Overrides the "Insert into post" phrase', 'advance-coupon'),
            'uploaded_to_this_item' => _x('Uploaded to this store', 'Overrides the "Uploaded to this post" phrase', 'advance-coupon'),
            'filter_items_list'     => _x('Filter stores list', 'Screen reader text for the filter links heading on the post type listing screen', 'advance-coupon'),
            'items_list_navigation' => _x('Stores list navigation', 'Screen reader text for the pagination heading on the post type listing screen', 'advance-coupon'),
            'items_list'            => _x('Stores list', 'Screen reader text for the items list heading on the post type listing screen', 'advance-coupon'),
        );

        $args = array(
            'labels'             => $labels,
            'public'             => true,
            'publicly_queryable' => true,
            'show_ui'            => true,
            'show_in_menu'       => true,
            'query_var'          => true,
            'rewrite'            => array('slug' => 'store'),
            'capability_type'    => 'post',
            'has_archive'        => true,
            'hierarchical'       => false,
            'menu_position'      => null,
            'menu_icon'          => 'dashicons-store',
            'supports'           => array('title', 'editor', 'thumbnail'),
            'taxonomies'         => array('store_category'),
        );

        register_post_type('store', $args);
    }

    /**
     * Register taxonomy relationship
     * This ensures the taxonomy is properly connected to the post type
     */
    public function register_taxonomy_relationship() {
        // Make sure the taxonomy exists
        if (!taxonomy_exists('store_category')) {
            return;
        }

        // Register the relationship
        register_taxonomy_for_object_type('store_category', 'store');
    }

    /**
     * Set custom columns for the admin list table
     */
    public function set_custom_columns($columns) {
        // Create a new array to hold our custom columns
        $new_columns = array();

        // Add the checkbox column
        if (isset($columns['cb'])) {
            $new_columns['cb'] = $columns['cb'];
        }

        // Add the title column
        if (isset($columns['title'])) {
            $new_columns['title'] = $columns['title'];
        }

        // Add our custom columns
        $new_columns['network'] = __('Network', 'advance-coupon');
        $new_columns['store_categories'] = __('Categories', 'advance-coupon');

        // Add the date column
        if (isset($columns['date'])) {
            $new_columns['date'] = $columns['date'];
        }

        // Add the image column
        $new_columns['image'] = __('Image', 'advance-coupon');

        return $new_columns;
    }

    /**
     * Reorder columns for the admin list table
     */
    public function reorder_columns($columns) {
        // We're already setting the order in set_custom_columns
        return $columns;
    }

    /**
     * Make columns sortable
     */
    public function sortable_columns($columns) {
        $columns['network'] = 'network';
        $columns['store_categories'] = 'store_categories';

        return $columns;
    }

    /**
     * Render custom column content
     */
    public function render_custom_columns($column, $post_id) {
        switch ($column) {
            case 'network':
                $network = get_post_meta($post_id, '_store_network', true);
                echo !empty($network) ? esc_html($network) : '—';
                break;

            case 'image':
                if (has_post_thumbnail($post_id)) {
                    echo get_the_post_thumbnail($post_id, array(50, 50));
                } else {
                    echo '—';
                }
                break;

            case 'store_categories':
                // Check if the taxonomy exists
                if (!taxonomy_exists('store_category')) {
                    echo '<span style="color:red;">Taxonomy not registered</span>';
                    break;
                }

                // Get all terms for this post using a direct database query
                global $wpdb;

                $query = $wpdb->prepare(
                    "SELECT t.term_id, t.name, t.slug
                    FROM {$wpdb->terms} AS t
                    INNER JOIN {$wpdb->term_taxonomy} AS tt ON t.term_id = tt.term_id
                    INNER JOIN {$wpdb->term_relationships} AS tr ON tr.term_taxonomy_id = tt.term_taxonomy_id
                    WHERE tt.taxonomy = %s AND tr.object_id = %d",
                    'store_category',
                    $post_id
                );

                $terms = $wpdb->get_results($query);

                if (!empty($terms)) {
                    $term_links = array();
                    foreach ($terms as $term) {
                        $term_links[] = '<a href="edit.php?post_type=store&store_category=' . esc_attr($term->slug) . '">' . esc_html($term->name) . '</a>';
                    }
                    echo implode(', ', $term_links);
                } else {
                    echo '—'; // No terms assigned to this post
                }
                break;
        }
    }
}
