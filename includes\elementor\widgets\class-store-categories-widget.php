<?php
/**
 * Store Categories Widget
 */

if (!defined('ABSPATH')) {
    exit; // Exit if accessed directly.
}

/**
 * Store Categories Widget
 */
class Store_Categories_Widget extends \Elementor\Widget_Base {

    /**
     * Get widget name.
     *
     * @return string Widget name.
     */
    public function get_name() {
        return 'store-categories';
    }

    /**
     * Get widget title.
     *
     * @return string Widget title.
     */
    public function get_title() {
        return __('Store Categories', 'advance-coupon');
    }

    /**
     * Get widget icon.
     *
     * @return string Widget icon.
     */
    public function get_icon() {
        return 'eicon-folder-o';
    }

    /**
     * Get widget categories.
     *
     * @return array Widget categories.
     */
    public function get_categories() {
        return ['advance-coupon'];
    }

    /**
     * Get widget keywords.
     *
     * @return array Widget keywords.
     */
    public function get_keywords() {
        return ['store', 'category', 'categories', 'grid', 'coupon'];
    }

    /**
     * Register widget controls.
     */
    protected function register_controls() {
        // Query Section
        $this->start_controls_section(
            'section_query',
            [
                'label' => __('Query', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_control(
            'show_all_categories',
            [
                'label' => __('Show All Categories', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
            ]
        );

        $this->add_control(
            'number',
            [
                'label' => __('Number of Categories', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 6,
                'min' => 1,
                'max' => 100,
                'step' => 1,
                'condition' => [
                    'show_all_categories' => 'no',
                ],
            ]
        );

        $this->add_control(
            'pagination',
            [
                'label' => __('Pagination', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'no',
                'condition' => [
                    'show_all_categories' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'categories_per_page',
            [
                'label' => __('Categories Per Page', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::NUMBER,
                'default' => 10,
                'min' => 1,
                'max' => 50,
                'step' => 1,
                'condition' => [
                    'show_all_categories' => 'yes',
                    'pagination' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'orderby',
            [
                'label' => __('Order By', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'name',
                'options' => [
                    'name' => __('Name', 'advance-coupon'),
                    'count' => __('Count', 'advance-coupon'),
                    'id' => __('ID', 'advance-coupon'),
                    'slug' => __('Slug', 'advance-coupon'),
                ],
            ]
        );

        $this->add_control(
            'order',
            [
                'label' => __('Order', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'ASC',
                'options' => [
                    'ASC' => __('Ascending', 'advance-coupon'),
                    'DESC' => __('Descending', 'advance-coupon'),
                ],
            ]
        );

        $this->add_control(
            'hide_empty',
            [
                'label' => __('Hide Empty', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();

        // Layout Section
        $this->start_controls_section(
            'section_layout',
            [
                'label' => __('Layout', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
            ]
        );

        $this->add_responsive_control(
            'column_width',
            [
                'label' => __('Column Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px'],
                'range' => [
                    'px' => [
                        'min' => 100,
                        'max' => 300,
                        'step' => 10,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 150,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-grid' => 'grid-template-columns: repeat(auto-fill, minmax({{SIZE}}{{UNIT}}, 1fr));',
                ],
            ]
        );

        $this->add_responsive_control(
            'column_gap',
            [
                'label' => __('Columns Gap', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'default' => [
                    'size' => 20,
                ],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-grid' => 'grid-column-gap: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_responsive_control(
            'row_gap',
            [
                'label' => __('Rows Gap', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'default' => [
                    'size' => 20,
                ],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 100,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-grid' => 'grid-row-gap: {{SIZE}}{{UNIT}}',
                ],
            ]
        );

        $this->add_control(
            'display_type',
            [
                'label' => __('Display Type', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'image',
                'options' => [
                    'image' => __('Image', 'advance-coupon'),
                    'icon' => __('Icon', 'advance-coupon'),
                ],
            ]
        );

        $this->add_control(
            'default_icon',
            [
                'label' => __('Default Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-tag',
                    'library' => 'fa-solid',
                ],
                'condition' => [
                    'display_type' => 'icon',
                ],
            ]
        );

        $this->add_control(
            'show_count',
            [
                'label' => __('Show Count', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SWITCHER,
                'label_on' => __('Yes', 'advance-coupon'),
                'label_off' => __('No', 'advance-coupon'),
                'return_value' => 'yes',
                'default' => 'yes',
            ]
        );

        $this->end_controls_section();

        // Box Style Section
        $this->start_controls_section(
            'section_box_style',
            [
                'label' => __('Box', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'box_width',
            [
                'label' => __('Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem', '%'],
                'range' => [
                    'px' => [
                        'min' => 50,
                        'max' => 300,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 150,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-category-item' => 'max-width: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'box_height',
            [
                'label' => __('Height', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem', '%'],
                'range' => [
                    'px' => [
                        'min' => 50,
                        'max' => 500,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 200,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-category-item' => 'height: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Background::get_type(),
            [
                'name' => 'box_background',
                'label' => __('Background', 'advance-coupon'),
                'types' => ['classic', 'gradient'],
                'selector' => '{{WRAPPER}} .store-category-item',
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'box_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-category-item',
            ]
        );

        $this->add_responsive_control(
            'box_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-item' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Box_Shadow::get_type(),
            [
                'name' => 'box_shadow',
                'label' => __('Box Shadow', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-category-item',
            ]
        );

        $this->add_responsive_control(
            'box_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-item' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'box_alignment',
            [
                'label' => __('Alignment', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::CHOOSE,
                'options' => [
                    'left' => [
                        'title' => __('Left', 'advance-coupon'),
                        'icon' => 'eicon-text-align-left',
                    ],
                    'center' => [
                        'title' => __('Center', 'advance-coupon'),
                        'icon' => 'eicon-text-align-center',
                    ],
                    'right' => [
                        'title' => __('Right', 'advance-coupon'),
                        'icon' => 'eicon-text-align-right',
                    ],
                ],
                'default' => 'center',
                'selectors' => [
                    '{{WRAPPER}} .store-category-item' => 'text-align: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'box_hover_animation',
            [
                'label' => __('Hover Animation', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::HOVER_ANIMATION,
            ]
        );

        $this->end_controls_section();

        // Image/Icon Style Section
        $this->start_controls_section(
            'section_image_icon_style',
            [
                'label' => __('Image/Icon', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_responsive_control(
            'image_width',
            [
                'label' => __('Width', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', '%'],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 500,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'default' => [
                    'unit' => '%',
                    'size' => 50,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-category-image img' => 'width: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'display_type' => 'image',
                ],
            ]
        );

        $this->add_responsive_control(
            'image_height',
            [
                'label' => __('Height', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem', '%'],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 500,
                    ],
                    '%' => [
                        'min' => 10,
                        'max' => 100,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-category-image img' => 'height: {{SIZE}}{{UNIT}}; object-fit: cover;',
                ],
                'condition' => [
                    'display_type' => 'image',
                ],
            ]
        );

        $this->add_responsive_control(
            'icon_size',
            [
                'label' => __('Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em', 'rem'],
                'range' => [
                    'px' => [
                        'min' => 10,
                        'max' => 200,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 50,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-category-icon i' => 'font-size: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .store-category-icon svg' => 'width: {{SIZE}}{{UNIT}}; height: {{SIZE}}{{UNIT}};',
                ],
                'condition' => [
                    'display_type' => 'icon',
                ],
            ]
        );

        $this->add_control(
            'icon_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-icon i' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .store-category-icon svg' => 'fill: {{VALUE}};',
                ],
                'condition' => [
                    'display_type' => 'icon',
                ],
            ]
        );

        $this->add_control(
            'icon_hover_color',
            [
                'label' => __('Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-item:hover .store-category-icon i' => 'color: {{VALUE}};',
                    '{{WRAPPER}} .store-category-item:hover .store-category-icon svg' => 'fill: {{VALUE}};',
                ],
                'condition' => [
                    'display_type' => 'icon',
                ],
            ]
        );

        $this->add_responsive_control(
            'image_icon_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-image, {{WRAPPER}} .store-category-icon' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'image_border',
                'label' => __('Border', 'advance-coupon'),
                'selector' => '{{WRAPPER}} .store-category-image img',
                'condition' => [
                    'display_type' => 'image',
                ],
            ]
        );

        $this->add_responsive_control(
            'image_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-image img' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
                'condition' => [
                    'display_type' => 'image',
                ],
            ]
        );

        $this->end_controls_section();

        // Title Style Section
        $this->start_controls_section(
            'section_title_style',
            [
                'label' => __('Title', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
            ]
        );

        $this->add_control(
            'title_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'title_hover_color',
            [
                'label' => __('Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-item:hover .store-category-title' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'title_typography',
                'selector' => '{{WRAPPER}} .store-category-title',
            ]
        );

        $this->add_responsive_control(
            'title_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-title' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();

        // Pagination Section
        $this->start_controls_section(
            'section_pagination',
            [
                'label' => __('Pagination', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_CONTENT,
                'condition' => [
                    'show_all_categories' => 'yes',
                    'pagination' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'prev_icon',
            [
                'label' => __('Previous Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-chevron-left',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'next_icon',
            [
                'label' => __('Next Icon', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::ICONS,
                'default' => [
                    'value' => 'fas fa-chevron-right',
                    'library' => 'fa-solid',
                ],
            ]
        );

        $this->add_control(
            'pagination_display',
            [
                'label' => __('Display Style', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SELECT,
                'default' => 'numbers',
                'options' => [
                    'numbers' => __('Numbers', 'advance-coupon'),
                    'numbers_with_prev_next' => __('Numbers with Prev/Next', 'advance-coupon'),
                ],
            ]
        );

        $this->end_controls_section();

        // Pagination Style Section
        $this->start_controls_section(
            'section_pagination_style',
            [
                'label' => __('Pagination', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_all_categories' => 'yes',
                    'pagination' => 'yes',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'pagination_typography',
                'selector' => '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span',
            ]
        );

        $this->add_responsive_control(
            'pagination_space_between',
            [
                'label' => __('Space Between', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em'],
                'range' => [
                    'px' => [
                        'min' => 0,
                        'max' => 50,
                    ],
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers li:not(:first-child)' => 'margin-left: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'pagination_icon_size',
            [
                'label' => __('Icon Size', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::SLIDER,
                'size_units' => ['px', 'em'],
                'range' => [
                    'px' => [
                        'min' => 8,
                        'max' => 50,
                    ],
                    'em' => [
                        'min' => 0.5,
                        'max' => 3,
                    ],
                ],
                'default' => [
                    'unit' => 'px',
                    'size' => 12,
                ],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers .next i, {{WRAPPER}} .store-categories-pagination .page-numbers .prev i' => 'font-size: {{SIZE}}{{UNIT}};',
                    '{{WRAPPER}} .store-categories-pagination .page-numbers .next svg, {{WRAPPER}} .store-categories-pagination .page-numbers .prev svg' => 'width: {{SIZE}}{{UNIT}}; height: {{SIZE}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'pagination_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->start_controls_tabs('pagination_style_tabs');

        $this->start_controls_tab(
            'pagination_style_normal',
            [
                'label' => __('Normal', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span:not(.current)' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span:not(.current)' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Border::get_type(),
            [
                'name' => 'pagination_border',
                'selector' => '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span:not(.current)',
            ]
        );

        $this->add_responsive_control(
            'pagination_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a, {{WRAPPER}} .store-categories-pagination .page-numbers span' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'pagination_style_hover',
            [
                'label' => __('Hover', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_hover_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a:hover' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_hover_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a:hover' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_hover_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers a:hover' => 'border-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->start_controls_tab(
            'pagination_style_active',
            [
                'label' => __('Active', 'advance-coupon'),
            ]
        );

        $this->add_control(
            'pagination_active_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers .current' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_active_bg_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers .current' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'pagination_active_border_color',
            [
                'label' => __('Border Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-categories-pagination .page-numbers .current' => 'border-color: {{VALUE}};',
                ],
            ]
        );

        $this->end_controls_tab();

        $this->end_controls_tabs();

        $this->end_controls_section();

        // Count Style Section
        $this->start_controls_section(
            'section_count_style',
            [
                'label' => __('Count', 'advance-coupon'),
                'tab' => \Elementor\Controls_Manager::TAB_STYLE,
                'condition' => [
                    'show_count' => 'yes',
                ],
            ]
        );

        $this->add_control(
            'count_color',
            [
                'label' => __('Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-count' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_control(
            'count_hover_color',
            [
                'label' => __('Hover Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'default' => '',
                'selectors' => [
                    '{{WRAPPER}} .store-category-item:hover .store-category-count' => 'color: {{VALUE}};',
                ],
            ]
        );

        $this->add_group_control(
            \Elementor\Group_Control_Typography::get_type(),
            [
                'name' => 'count_typography',
                'selector' => '{{WRAPPER}} .store-category-count',
            ]
        );

        $this->add_responsive_control(
            'count_margin',
            [
                'label' => __('Margin', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-count' => 'margin: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_control(
            'count_background_color',
            [
                'label' => __('Background Color', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::COLOR,
                'selectors' => [
                    '{{WRAPPER}} .store-category-count' => 'background-color: {{VALUE}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'count_padding',
            [
                'label' => __('Padding', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', 'em', '%'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-count' => 'padding: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->add_responsive_control(
            'count_border_radius',
            [
                'label' => __('Border Radius', 'advance-coupon'),
                'type' => \Elementor\Controls_Manager::DIMENSIONS,
                'size_units' => ['px', '%', 'em'],
                'selectors' => [
                    '{{WRAPPER}} .store-category-count' => 'border-radius: {{TOP}}{{UNIT}} {{RIGHT}}{{UNIT}} {{BOTTOM}}{{UNIT}} {{LEFT}}{{UNIT}};',
                ],
            ]
        );

        $this->end_controls_section();
    }

    /**
     * Render widget output on the frontend.
     */
    protected function render() {
        $settings = $this->get_settings_for_display();

        // Generate a unique ID for this widget instance
        $widget_id = 'store-categories-widget-' . $this->get_id();

        // Pagination setup
        $paged = (get_query_var('paged')) ? get_query_var('paged') : 1;
        if (isset($_GET['category-page'])) {
            $paged = intval($_GET['category-page']);
        }

        // Determine query parameters based on settings
        if ($settings['show_all_categories'] === 'yes') {
            // Archive mode: Show all categories with pagination
            $number = $settings['pagination'] === 'yes' ? intval($settings['categories_per_page']) : 0; // 0 means all
        } else {
            // Normal mode: Use the Number of Categories setting
            $number = intval($settings['number']);
        }

        // Query args
        $args = array(
            'taxonomy' => 'store_category',
            'orderby' => $settings['orderby'],
            'order' => $settings['order'],
            'hide_empty' => ($settings['hide_empty'] === 'yes'),
            'number' => $number,
            'offset' => $settings['pagination'] === 'yes' ? ($paged - 1) * $number : 0,
        );

        // For pagination, we need to get the total count
        $total_terms = 0;
        if ($settings['pagination'] === 'yes') {
            // Get total count for pagination
            $count_args = array(
                'taxonomy' => 'store_category',
                'hide_empty' => ($settings['hide_empty'] === 'yes'),
                'fields' => 'count',
            );
            $total_terms = get_terms($count_args);
        }

        // Get categories
        $categories = get_terms($args);

        if (empty($categories) || is_wp_error($categories)) {
            echo '<div class="elementor-alert elementor-alert-info">';
            echo __('No store categories found.', 'advance-coupon');
            echo '</div>';
            return;
        }

        // Get animation class
        $animation_class = !empty($settings['box_hover_animation']) ? 'elementor-animation-' . $settings['box_hover_animation'] : '';

        // Start output
        echo '<div class="store-categories-container" id="' . esc_attr($widget_id) . '">';

        ?>
        <div class="store-categories-grid">
            <?php foreach ($categories as $category) : ?>
                <?php
                // Get term meta for image or icon
                $term_meta = get_term_meta($category->term_id, 'category_image', true);
                $term_icon = get_term_meta($category->term_id, 'category_icon', true);
                $image_url = '';

                if (!empty($term_meta)) {
                    $image_url = wp_get_attachment_image_url($term_meta, 'medium');
                }
                ?>
                <a href="<?php echo esc_url(get_term_link($category)); ?>" class="store-category-item <?php echo esc_attr($animation_class); ?>">
                    <?php if ($settings['display_type'] === 'image') : ?>
                        <div class="store-category-image">
                            <?php if (!empty($image_url)) : ?>
                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($category->name); ?>">
                            <?php else : ?>
                                <img src="<?php echo esc_url(ADVCOUPON_PLUGIN_URL . 'assets/images/placeholder.svg'); ?>" alt="<?php echo esc_attr($category->name); ?>">
                            <?php endif; ?>
                        </div>
                    <?php else : ?>
                        <div class="store-category-icon">
                            <?php if (!empty($term_icon)) : ?>
                                <i class="<?php echo esc_attr($term_icon); ?>"></i>
                            <?php else : ?>
                                <?php \Elementor\Icons_Manager::render_icon($settings['default_icon'], ['aria-hidden' => 'true']); ?>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <h3 class="store-category-title"><?php echo esc_html($category->name); ?></h3>

                    <?php if ($settings['show_count'] === 'yes') : ?>
                        <div class="store-category-count">
                            <?php echo esc_html($category->count); ?> <?php echo _n('Store', 'Stores', $category->count, 'advance-coupon'); ?>
                        </div>
                    <?php endif; ?>
                </a>
            <?php endforeach; ?>
        </div>
        <?php

        // Add pagination if enabled and there are multiple pages
        if ($settings['pagination'] === 'yes' && $total_terms > $number) {
            $max_num_pages = ceil($total_terms / $number);

            echo '<div class="store-categories-pagination">';

            $big = 999999999; // need an unlikely integer

            // Get previous and next icons
            $prev_icon = '';
            if (!empty($settings['prev_icon']['value'])) {
                ob_start();
                \Elementor\Icons_Manager::render_icon($settings['prev_icon'], ['aria-hidden' => 'true']);
                $prev_icon = ob_get_clean();
            } else {
                $prev_icon = '<i class="fas fa-chevron-left"></i>';
            }

            $next_icon = '';
            if (!empty($settings['next_icon']['value'])) {
                ob_start();
                \Elementor\Icons_Manager::render_icon($settings['next_icon'], ['aria-hidden' => 'true']);
                $next_icon = ob_get_clean();
            } else {
                $next_icon = '<i class="fas fa-chevron-right"></i>';
            }

            // Pagination args
            $pagination_args = array(
                'base' => add_query_arg('category-page', '%#%'),
                'format' => '?category-page=%#%',
                'current' => max(1, $paged),
                'total' => $max_num_pages,
                'prev_text' => $prev_icon,
                'next_text' => $next_icon,
                'type' => 'list',
            );

            // If display style is numbers only, remove prev/next
            if ($settings['pagination_display'] === 'numbers') {
                $pagination_args['prev_next'] = false;
            }

            // Generate the pagination links
            echo paginate_links($pagination_args);

            echo '</div>';
        }

        echo '</div>'; // Close store-categories-container
    }
}
