/**
 * Related Stores Widget Styles
 *
 * These styles override the default Slick slider styles
 */

/* Utility class for screen reader text */
.screen-reader-text {
    border: 0;
    clip: rect(1px, 1px, 1px, 1px);
    clip-path: inset(50%);
    height: 1px;
    margin: -1px;
    overflow: hidden;
    padding: 0;
    position: absolute;
    width: 1px;
    word-wrap: normal !important;
}

/* Override Slick theme defaults but keep our custom icons */
.slick-slider .slick-prev:before,
.slick-slider .slick-next:before {
    content: none !important;
}

.slick-dots li button:before {
    content: none !important;
}

/* Force arrows to be visible */
.slick-prev, .slick-next {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Add explicit arrow styles */
.slick-prev {
    left: -25px !important;
    z-index: 999 !important;
}

.slick-next {
    right: -25px !important;
    z-index: 999 !important;
}

/* Widget container */
.related-stores-widget {
    margin-bottom: 30px;
}

/* Widget title */
.related-stores-title {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
}

/* Carousel container */
.related-stores-carousel {
    position: relative;
    margin-bottom: 20px;
}

/* Store item */
.related-stores-carousel .store-item {
    padding: 20px;
    border: none;
    border-radius: 12px;
    background-color: #fff;
    margin: 10px;
    transition: all 0.3s ease;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.08);
    overflow: hidden;
}

.related-stores-carousel .store-item:hover {
    box-shadow: 0 8px 20px rgba(0,0,0,0.15);
    transform: translateY(-5px);
}

/* Store thumbnail */
.related-stores-carousel .store-thumbnail {
    margin-bottom: 20px;
    height: 140px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.related-stores-carousel .store-item:hover .store-thumbnail {
    background-color: #f1f3f5;
}

.related-stores-carousel .store-thumbnail img {
    max-height: 100%;
    width: auto;
    max-width: 100%;
    object-fit: contain;
    transition: all 0.3s ease;
}

.related-stores-carousel .store-item:hover .store-thumbnail img {
    transform: scale(1.05);
}

/* Store title wrapper */
.related-stores-carousel .store-title-wrapper {
    margin-bottom: 15px;
    position: relative;
}

.related-stores-carousel .store-title-wrapper.has-badge {
    padding-right: 70px;
}

/* Store title */
.related-stores-carousel .store-title {
    font-size: 16px;
    font-weight: 600;
    margin: 0;
    line-height: 1.4;
}

.related-stores-carousel .store-title a {
    color: #333;
    text-decoration: none;
}

.related-stores-carousel .store-title a:hover {
    color: #4dabf7;
}

/* Coupon count badge */
.related-stores-carousel .coupon-count-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #4dabf7;
    color: #fff;
    font-size: 12px;
    padding: 5px 10px;
    border-radius: 20px;
    font-weight: 600;
    box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    transform: translateY(-50%);
    letter-spacing: 0.5px;
}

/* Store actions */
.related-stores-carousel .store-actions {
    margin-top: 15px;
}

/* View store button */
.related-stores-carousel .view-store {
    display: inline-block;
    padding: 10px 20px;
    background-color: #4dabf7;
    color: #fff;
    border-radius: 50px;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(77, 171, 247, 0.3);
    position: relative;
    overflow: hidden;
}

.related-stores-carousel .view-store:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to right, rgba(255,255,255,0.1), rgba(255,255,255,0.2));
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.related-stores-carousel .view-store:hover {
    background-color: #339af0;
    transform: translateY(-3px);
    box-shadow: 0 6px 15px rgba(77, 171, 247, 0.4);
}

.related-stores-carousel .view-store:hover:before {
    transform: translateX(100%);
}

/* Slick carousel customization */
.related-stores-carousel .slick-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 10;
    width: 44px;
    height: 44px;
    background-color: #4dabf7; /* Default background color */
    border-radius: 50%;
    box-shadow: 0 3px 8px rgba(0,0,0,0.15);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    border: none;
    outline: none;
    transition: all 0.3s ease;
}

/* Hide the text content of the buttons but keep the icon visible */
.related-stores-carousel .slick-prev,
.related-stores-carousel .slick-next {
    font-size: 0;
    line-height: 0;
    color: transparent;
}

.related-stores-carousel .slick-arrow i {
    line-height: 1;
    display: block !important;
    visibility: visible !important;
    /* Remove fixed font-size and color to allow Elementor controls to work */
}

.related-stores-carousel .slick-arrow:hover {
    /* Slightly darken whatever background color is set */
    filter: brightness(0.9);
    box-shadow: 0 5px 12px rgba(0,0,0,0.2);
    transform: translateY(-50%) scale(1.05);
}

.related-stores-carousel .slick-prev {
    left: -22px;
    opacity: 1 !important;
    visibility: visible !important;
}

.related-stores-carousel .slick-next {
    right: -22px;
    opacity: 1 !important;
    visibility: visible !important;
}

.related-stores-carousel .slick-dots {
    position: relative;
    bottom: 0;
    display: flex;
    justify-content: center;
    list-style: none;
    margin: 20px 0 0;
    padding: 0;
}

.related-stores-carousel .slick-dots li {
    margin: 0 4px;
    line-height: 0;
}

/* Hide the default button text */
.related-stores-carousel .slick-dots li button {
    width: 12px;
    height: 6px;
    border-radius: 3px;
    background-color: #e0e0e0;
    border: none;
    font-size: 0;
    line-height: 0;
    padding: 0;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    color: transparent;
    text-indent: -9999px;
    overflow: hidden;
}

/* Remove the default slick button before content */
.related-stores-carousel .slick-dots li button:before {
    content: none;
}

.related-stores-carousel .slick-dots li.slick-active button {
    background-color: #4dabf7;
    width: 24px;
}

/* Error message */
.related-stores-error {
    padding: 20px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    color: #868e96;
    text-align: center;
}

/* Responsive styles */
@media (max-width: 767px) {
    .related-stores-carousel .slick-arrow {
        width: 36px;
        height: 36px;
    }

    .related-stores-carousel .slick-arrow i {
        font-size: 14px;
    }

    .related-stores-carousel .slick-prev {
        left: -18px;
    }

    .related-stores-carousel .slick-next {
        right: -18px;
    }

    .related-stores-carousel .store-item {
        margin: 5px;
        padding: 15px;
    }

    .related-stores-carousel .store-thumbnail {
        height: 120px;
        margin-bottom: 15px;
    }

    .related-stores-carousel .store-title {
        font-size: 14px;
    }

    .related-stores-carousel .view-store {
        padding: 8px 16px;
        font-size: 13px;
    }

    .related-stores-carousel .slick-dots {
        margin-top: 15px;
    }

    .related-stores-carousel .slick-dots li button {
        width: 10px;
        height: 5px;
    }

    .related-stores-carousel .slick-dots li.slick-active button {
        width: 20px;
    }

    /* Fix for mobile navigation text */
    .related-stores-carousel .slick-prev,
    .related-stores-carousel .slick-next {
        text-indent: -9999px;
        overflow: hidden;
    }

    /* Ensure dots don't have default styling on mobile */
    .related-stores-carousel .slick-dots li button:before {
        content: none !important;
    }
}
