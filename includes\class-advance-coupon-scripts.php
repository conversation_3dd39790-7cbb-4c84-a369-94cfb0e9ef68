<?php
/**
 * <PERSON><PERSON><PERSON> and Styles Handler for Advance Coupon
 */
class Advance_Coupon_Scripts {

    /**
     * Holds the instance of this class
     */
    private static $instance = null;

    /**
     * Get the instance of this class
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        // Register and enqueue frontend scripts and styles
        add_action('wp_enqueue_scripts', array($this, 'register_frontend_scripts'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));

        // Register and enqueue admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'register_admin_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Register and enqueue Elementor widget scripts and styles
        add_action('elementor/frontend/after_register_scripts', array($this, 'register_widget_scripts'));
        add_action('elementor/frontend/after_enqueue_styles', array($this, 'enqueue_widget_styles'));
    }

    /**
     * Register frontend scripts and styles
     */
    public function register_frontend_scripts() {
        // Register styles
        wp_register_style('advance-coupon-store-categories', ADVCOUPON_PLUGIN_URL . 'assets/css/store-categories-widget.css', array(), ADVCOUPON_VERSION);

        // Register scripts
        // Add more scripts here as needed
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts() {
        // Enqueue styles
        wp_enqueue_style('advance-coupon-store-categories');

        // Enqueue scripts
        // Add more scripts here as needed
    }

    /**
     * Register admin scripts and styles
     */
    public function register_admin_scripts() {
        // Register styles
        // Add admin styles here as needed

        // Register scripts
        wp_register_script('advance-coupon-category-meta', ADVCOUPON_PLUGIN_URL . 'assets/js/category-meta.js', array('jquery'), ADVCOUPON_VERSION, true);

        // Localize scripts
        wp_localize_script('advance-coupon-category-meta', 'advanceCouponCategoryMeta', array(
            'placeholderUrl' => ADVCOUPON_PLUGIN_URL . 'assets/images/placeholder.svg',
        ));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Only load on specific admin pages if needed
        // For now, we'll let the category meta class handle its own scripts
    }

    /**
     * Register widget scripts
     */
    public function register_widget_scripts() {
        // Register widget scripts
        // Add widget scripts here as needed
    }

    /**
     * Enqueue widget styles
     */
    public function enqueue_widget_styles() {
        // Enqueue widget styles
        wp_enqueue_style('advance-coupon-store-categories');
    }
}

// Initialize the class
function initialize_advance_coupon_scripts() {
    Advance_Coupon_Scripts::instance();
}
add_action('init', 'initialize_advance_coupon_scripts', 5);
