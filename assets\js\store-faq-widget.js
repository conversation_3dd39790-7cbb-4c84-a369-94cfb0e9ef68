/**
 * Store FAQ Widget JavaScript
 */
(function($) {
    'use strict';

    // Initialize the FAQ accordion
    function initStoreFaqWidget() {
        $('.store-faq-widget').each(function() {
            var $widget = $(this);
            // Get the allow-multiple setting directly from the attribute
            // This is more reliable than using .data() which can cache values
            var allowMultiple = $widget.attr('data-allow-multiple') === 'true';

            // Debug output
            // console.log('Widget ID:', $widget.attr('id'));
            // console.log('Allow Multiple:', allowMultiple);
            // console.log('Data Attribute:', $widget.attr('data-allow-multiple'));

            // Initialize the first item if it should be open
            var $firstHeader = $widget.find('.store-faq-item-header').first();
            var $firstContent = $firstHeader.closest('.store-faq-item').find('.store-faq-item-content');

            // Check if the first item has the 'active' class (set in PHP)
            if ($firstHeader.hasClass('active')) {
                // Make sure the content is visible
                $firstContent.show();
                toggleIcon($firstHeader, true);
            }

            // Handle click on FAQ header
            $widget.find('.store-faq-item-header').off('click').on('click', function() {
                var $header = $(this);
                var $item = $header.closest('.store-faq-item');
                var $content = $item.find('.store-faq-item-content');
                var isActive = $header.hasClass('active');

                // If not allowing multiple open items, close all others
                if (!allowMultiple) {
                    $widget.find('.store-faq-item-header.active').not($header).removeClass('active');
                    $widget.find('.store-faq-item-content').not($content).slideUp(300);

                    // Update icons for all other items
                    $widget.find('.store-faq-item-header').not($header).each(function() {
                        toggleIcon($(this), false);
                    });
                }

                // Toggle current item
                if (isActive) {
                    $header.removeClass('active');
                    $content.slideUp(300);
                    toggleIcon($header, false);
                } else {
                    $header.addClass('active');
                    $content.slideDown(300);
                    toggleIcon($header, true);
                }
            });
        });
    }

    // Toggle the icon based on active state
    function toggleIcon($header, isActive) {
        var $iconContainer = $header.find('.store-faq-item-icon');
        var $expandedIcon = $iconContainer.find('.icon-expanded');
        var $collapsedIcon = $iconContainer.find('.icon-collapsed');

        if (isActive) {
            $expandedIcon.show();
            $collapsedIcon.hide();
        } else {
            $expandedIcon.hide();
            $collapsedIcon.show();
        }
    }

    // Initialize when document is ready
    $(document).ready(function() {
        initStoreFaqWidget();

        // Also initialize when Elementor frontend is initialized (for Elementor editor)
        $(document).on('elementor/frontend/init', function() {
            if (typeof elementorFrontend !== 'undefined') {
                elementorFrontend.hooks.addAction('frontend/element_ready/store_faq_widget.default', function() {
                    initStoreFaqWidget();
                });
            }
        });
    });

})(jQuery);
