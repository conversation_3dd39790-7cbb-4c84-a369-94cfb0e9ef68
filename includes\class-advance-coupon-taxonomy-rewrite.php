<?php
/**
 * Taxonomy URL Rewriter for Advance Coupon
 *
 * This class handles rewriting taxonomy URLs to use page slugs
 */
class Advance_Coupon_Taxonomy_Rewrite {

    /**
     * Holds the instance of this class
     */
    private static $instance = null;

    /**
     * Get the instance of this class
     */
    public static function instance() {
        if (is_null(self::$instance)) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    public function __construct() {
        // Add admin menu
        add_action('admin_menu', array($this, 'add_admin_menu'));

        // Register settings
        add_action('admin_init', array($this, 'register_settings'));

        // Add rewrite rules
        add_action('init', array($this, 'add_rewrite_rules'), 10);

        // Filter template include
        add_filter('template_include', array($this, 'template_include'), 99);

        // Filter term link
        add_filter('term_link', array($this, 'filter_term_link'), 10, 3);

        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));

        // Parse request
        add_action('parse_request', array($this, 'parse_request'));

        // <PERSON>le redirects from old URLs to new URLs
        add_action('template_redirect', array($this, 'redirect_old_urls'));

        // Flush rewrite rules when settings are saved
        add_action('update_option_advance_coupon_taxonomy_rewrite', array($this, 'flush_rewrite_rules'));

        // Add term info to page
        // add_action('wp_head', array($this, 'add_term_info_to_page'));

        // Filter store CPT registration
        add_filter('register_post_type_args', array($this, 'modify_store_cpt_args'), 10, 2);

        // Filter store CPT permalink
        add_filter('post_type_link', array($this, 'filter_store_permalink'), 10, 2);
    }

    /**
     * Add admin menu
     */
    public function add_admin_menu() {
        // Add the menu under Stores
        add_submenu_page(
            'edit.php?post_type=store',
            __('Taxonomy URL Rewriter', 'advance-coupon'),
            __('Taxonomy URL Rewriter', 'advance-coupon'),
            'manage_options',
            'advance-coupon-taxonomy-rewrite',
            array($this, 'render_admin_page')
        );

        // Also add it under Settings for easier access
        add_options_page(
            __('Taxonomy URL Rewriter', 'advance-coupon'),
            __('Taxonomy URL Rewriter', 'advance-coupon'),
            'manage_options',
            'advance-coupon-taxonomy-rewrite',
            array($this, 'render_admin_page')
        );
    }

    /**
     * Register settings
     */
    public function register_settings() {
        register_setting(
            'advance_coupon_taxonomy_rewrite_group',
            'advance_coupon_taxonomy_rewrite',
            array(
                'sanitize_callback' => array($this, 'sanitize_settings'),
                'default' => array(
                    'enabled' => 'no',
                    'page_id' => 0,
                    'store_cpt_slug' => 'store',
                ),
            )
        );
    }

    /**
     * Sanitize settings
     */
    public function sanitize_settings($input) {
        $sanitized = array();

        // Sanitize enabled
        $sanitized['enabled'] = isset($input['enabled']) && $input['enabled'] === 'yes' ? 'yes' : 'no';

        // Sanitize page ID
        $sanitized['page_id'] = isset($input['page_id']) ? absint($input['page_id']) : 0;

        // Sanitize store CPT slug
        if (isset($input['store_cpt_slug'])) {
            $store_cpt_slug = sanitize_title($input['store_cpt_slug']);
            // Make sure the slug is not empty
            $sanitized['store_cpt_slug'] = !empty($store_cpt_slug) ? $store_cpt_slug : 'store';
        } else {
            $sanitized['store_cpt_slug'] = 'store';
        }

        return $sanitized;
    }

    /**
     * Render admin page
     */
    public function render_admin_page() {
        // Get current settings
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
            'store_cpt_slug' => 'store',
        ));

        // Get all pages
        $pages = get_pages(array(
            'post_status' => 'publish',
            'sort_column' => 'post_title',
            'sort_order' => 'ASC',
        ));

        ?>
        <div class="wrap">
            <h1><?php echo esc_html__('URL Rewriter Settings', 'advance-coupon'); ?></h1>
            <p><?php echo esc_html__('Change the URL structure of taxonomies and post types.', 'advance-coupon'); ?></p>

            <form method="post" action="options.php">
                <?php settings_fields('advance_coupon_taxonomy_rewrite_group'); ?>
                <?php do_settings_sections('advance_coupon_taxonomy_rewrite_group'); ?>

                <h2><?php echo esc_html__('Taxonomy URL Rewriter', 'advance-coupon'); ?></h2>
                <p><?php echo esc_html__('Change the URL structure of store_category taxonomy to use a page slug.', 'advance-coupon'); ?></p>

                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php echo esc_html__('Enable URL Rewriting', 'advance-coupon'); ?></th>
                        <td>
                            <label>
                                <input type="checkbox" name="advance_coupon_taxonomy_rewrite[enabled]" value="yes" <?php checked($settings['enabled'], 'yes'); ?> />
                                <?php echo esc_html__('Enable custom URL structure for store_category taxonomy', 'advance-coupon'); ?>
                            </label>
                        </td>
                    </tr>

                    <tr valign="top">
                        <th scope="row"><?php echo esc_html__('Select Page', 'advance-coupon'); ?></th>
                        <td>
                            <select name="advance_coupon_taxonomy_rewrite[page_id]">
                                <option value="0"><?php echo esc_html__('-- Select a Page --', 'advance-coupon'); ?></option>
                                <?php foreach ($pages as $page) : ?>
                                    <option value="<?php echo esc_attr($page->ID); ?>" <?php selected($settings['page_id'], $page->ID); ?>>
                                        <?php echo esc_html($page->post_title); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                            <p class="description">
                                <?php echo esc_html__('Select a page whose slug will be used in URLs instead of "store-category".', 'advance-coupon'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <h2><?php echo esc_html__('Store CPT Slug Changer', 'advance-coupon'); ?></h2>
                <p><?php echo esc_html__('Change the URL slug for the Store custom post type.', 'advance-coupon'); ?></p>

                <table class="form-table">
                    <tr valign="top">
                        <th scope="row"><?php echo esc_html__('Store CPT Slug', 'advance-coupon'); ?></th>
                        <td>
                            <input type="text" name="advance_coupon_taxonomy_rewrite[store_cpt_slug]" value="<?php echo esc_attr($settings['store_cpt_slug']); ?>" class="regular-text" />
                            <p class="description">
                                <?php echo esc_html__('Enter the slug to use for Store URLs. Default is "store".', 'advance-coupon'); ?>
                                <?php echo esc_html__('Example: If you enter "promo", URLs will be /promo/store-name/ instead of /store/store-name/.', 'advance-coupon'); ?>
                            </p>
                        </td>
                    </tr>
                </table>

                <?php submit_button(); ?>
            </form>

            <div class="notice notice-info">
                <p>
                    <strong><?php echo esc_html__('Important:', 'advance-coupon'); ?></strong>
                    <?php echo esc_html__('After saving these settings, please go to Settings > Permalinks and click "Save Changes" to update the rewrite rules.', 'advance-coupon'); ?>
                </p>
            </div>
        </div>
        <?php
    }

    /**
     * Add rewrite rules
     */
    public function add_rewrite_rules() {
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
        ));

        // Only add rewrite rules if enabled and a page is selected
        if ($settings['enabled'] !== 'yes' || empty($settings['page_id'])) {
            return;
        }

        // Get the page slug
        $page = get_post($settings['page_id']);
        if (!$page) {
            return;
        }

        $page_slug = $page->post_name;

        // Add rewrite rule for the main archive
        add_rewrite_rule(
            '^' . $page_slug . '/?$',
            'index.php?pagename=' . $page_slug,
            'top'
        );

        // Add rewrite rule for individual terms
        add_rewrite_rule(
            '^' . $page_slug . '/([^/]+)/?$',
            'index.php?store_category=$matches[1]&advance_coupon_page_slug=' . $page_slug,
            'top'
        );

        // Add rewrite rule for pagination
        add_rewrite_rule(
            '^' . $page_slug . '/([^/]+)/page/([0-9]{1,})/?$',
            'index.php?store_category=$matches[1]&paged=$matches[2]&advance_coupon_page_slug=' . $page_slug,
            'top'
        );
    }

    /**
     * Add query vars
     */
    public function add_query_vars($vars) {
        $vars[] = 'advance_coupon_page_slug';
        return $vars;
    }

    /**
     * Parse request
     */
    public function parse_request($wp) {
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
        ));

        // Only process if enabled and a page is selected
        if ($settings['enabled'] !== 'yes' || empty($settings['page_id'])) {
            return;
        }

        // Check if we're on a rewritten URL
        if (isset($wp->query_vars['advance_coupon_page_slug'])) {
            // This is a rewritten URL, make sure WordPress knows it's a taxonomy archive
            $wp->query_vars['taxonomy'] = 'store_category';
        }
    }

    /**
     * Filter term link
     */
    public function filter_term_link($link, $term, $taxonomy) {
        // Only process store_category taxonomy
        if ($taxonomy !== 'store_category') {
            return $link;
        }

        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
        ));

        // Only process if enabled and a page is selected
        if ($settings['enabled'] !== 'yes' || empty($settings['page_id'])) {
            return $link;
        }

        // Get the page slug
        $page = get_post($settings['page_id']);
        if (!$page) {
            return $link;
        }

        $page_slug = $page->post_name;

        // Replace the taxonomy slug with the page slug
        $taxonomy_slug = get_taxonomy($taxonomy)->rewrite['slug'];
        return str_replace('/' . $taxonomy_slug . '/', '/' . $page_slug . '/', $link);
    }

    /**
     * Template include
     */
    public function template_include($template) {
        // Check if we're on a rewritten URL
        if (get_query_var('advance_coupon_page_slug') && is_tax('store_category')) {
            // We're on a rewritten URL, use the taxonomy template
            return $template;
        }

        // Check if we're on the main page
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
        ));

        // Only process if enabled and a page is selected
        if ($settings['enabled'] !== 'yes' || empty($settings['page_id'])) {
            return $template;
        }

        // Check if we're on the main page
        if (is_page($settings['page_id'])) {
            // We're on the main page, add a filter to modify the content
            add_filter('the_content', array($this, 'modify_page_content'), 999);
        }

        return $template;
    }

    /**
     * Modify page content
     */
    public function modify_page_content($content) {
        // We're not modifying the page content anymore
        // This function is kept for future use if needed
        return $content;
    }

    /**
     * Add term info to page
     */
    public function add_term_info_to_page() {
        // Check if we're on a rewritten URL
        if (!get_query_var('advance_coupon_page_slug') || !is_tax('store_category')) {
            return;
        }

        // Get the current term
        $term = get_queried_object();

        if (!$term || !isset($term->term_id)) {
            return;
        }

        // Add CSS for term info
        ?>
        <style type="text/css">
            /* Store Category Info Styles */
            /* .store-category-info {
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 1px solid #e0e0e0;
            }

            .store-category-title {
                font-size: 28px;
                margin-bottom: 15px;
                color: #333;
            }

            .store-category-description {
                color: #666;
                line-height: 1.6;
                margin-bottom: 20px;
            } */
        </style>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Create the term info HTML
            var termInfo = '<div class="store-category-info">';
            termInfo += '<h1 class="store-category-title"><?php echo esc_js($term->name); ?></h1>';

            <?php if (!empty($term->description)) : ?>
            termInfo += '<div class="store-category-description"><?php echo esc_js($term->description); ?></div>';
            <?php endif; ?>

            termInfo += '</div>';

            // Add the term info to the page
            // Try different selectors based on theme structure
            var $contentContainer = $('.entry-content').first();
            if ($contentContainer.length === 0) {
                $contentContainer = $('.elementor-widget-container').first();
            }
            if ($contentContainer.length === 0) {
                $contentContainer = $('#content').first();
            }
            if ($contentContainer.length === 0) {
                $contentContainer = $('article').first();
            }

            if ($contentContainer.length > 0) {
                $contentContainer.prepend(termInfo);
            }
        });
        </script>
        <?php
    }

    /**
     * Redirect old URLs to new URLs
     */
    public function redirect_old_urls() {
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'enabled' => 'no',
            'page_id' => 0,
        ));

        // Only process if enabled and a page is selected
        if ($settings['enabled'] !== 'yes' || empty($settings['page_id'])) {
            return;
        }

        // Get the page slug
        $page = get_post($settings['page_id']);
        if (!$page) {
            return;
        }

        $page_slug = $page->post_name;

        // Check if we're on a store_category taxonomy page with the original URL structure
        if (is_tax('store_category') && !get_query_var('advance_coupon_page_slug')) {
            // Get the current term
            $term = get_queried_object();

            if ($term && isset($term->slug)) {
                // Build the new URL
                $new_url = home_url($page_slug . '/' . $term->slug . '/');

                // Add pagination if needed
                $paged = get_query_var('paged');
                if ($paged > 1) {
                    $new_url = trailingslashit($new_url) . 'page/' . $paged . '/';
                }

                // Redirect to the new URL
                wp_redirect($new_url, 301);
                exit;
            }
        }
    }

    /**
     * Flush rewrite rules
     */
    public function flush_rewrite_rules() {
        flush_rewrite_rules();
    }

    /**
     * Modify store CPT arguments
     */
    public function modify_store_cpt_args($args, $post_type) {
        // Only modify the 'store' post type
        if ($post_type !== 'store') {
            return $args;
        }

        // Get settings
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'store_cpt_slug' => 'store',
        ));

        // Get the custom slug
        $custom_slug = !empty($settings['store_cpt_slug']) ? sanitize_title($settings['store_cpt_slug']) : 'store';

        // Modify the rewrite args
        if (isset($args['rewrite']) && is_array($args['rewrite'])) {
            $args['rewrite']['slug'] = $custom_slug;
        } else {
            $args['rewrite'] = array(
                'slug' => $custom_slug,
                'with_front' => true,
                'pages' => true,
                'feeds' => true,
            );
        }

        return $args;
    }

    /**
     * Filter store permalink
     */
    public function filter_store_permalink($post_link, $post) {
        // Only filter 'store' post type
        if ($post->post_type !== 'store') {
            return $post_link;
        }

        // Get settings
        $settings = get_option('advance_coupon_taxonomy_rewrite', array(
            'store_cpt_slug' => 'store',
        ));

        // Get the custom slug
        $custom_slug = !empty($settings['store_cpt_slug']) ? sanitize_title($settings['store_cpt_slug']) : 'store';

        // Replace the default slug with the custom slug
        $post_link = str_replace('/store/', '/' . $custom_slug . '/', $post_link);

        return $post_link;
    }
}

// Initialize the class
function initialize_advance_coupon_taxonomy_rewrite() {
    Advance_Coupon_Taxonomy_Rewrite::instance();
}
add_action('init', 'initialize_advance_coupon_taxonomy_rewrite', 5);
