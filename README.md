# Advance Coupon

A WordPress plugin for managing stores and coupons with Elementor compatibility.

## Description

Advance Coupon is a WordPress plugin that allows you to create and manage stores and coupons on your website. The plugin is compatible with Elementor and provides widgets to display stores and coupons on your pages.

## Features

### Stores
- Custom post type for stores
- Store details: name, description, image, URL, network
- Dynamic FAQ fields with rich text editor
- Network filter in admin

### Coupons
- Custom post type for coupons (without public URLs)
- Coupon details: name, expiration time, type, exclusive/verified status, affiliate link
- Conditional fields based on coupon type:
  - Online Sale: Coupon link
  - Online Code: Specific link, code
  - In-store Code: Printable image
- Categories and tags for coupons
- Relationship with stores (one coupon belongs to one store)

### Elementor Integration
- Coupon widget with filtering options
- Store widget with filtering options
- Store Description widget for individual store pages
- Store Coupons widget for displaying coupons from a specific store
- Customizable styles for all widgets

## Installation

1. Upload the `advance-coupon` folder to the `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. Start creating stores and coupons from the WordPress admin

## Usage

### Creating Stores
1. Go to Stores > Add New
2. Enter store name, description, and upload an image
3. Fill in store URL and network
4. Assign store categories to organize your stores
5. Add FAQs by specifying the number of FAQ fields you want
6. Publish the store

### Creating Coupons
1. Go to Coupons > Add New
2. Enter coupon name
3. Select the store this coupon belongs to (the coupon will automatically inherit the store's categories)
4. Set expiration time
5. Choose coupon type (Online Sale, Online Code, In-store Code)
6. Fill in the fields based on the selected coupon type
7. Publish the coupon

### Using Elementor Widgets
1. Edit a page with Elementor
2. Find the "Coupons", "Stores", "Store Description", "Store Coupons", or "Store FAQs" widget in the widget panel
3. Drag and drop the widget to your page
4. Configure the widget settings
5. Save the page

#### Store Coupons Widget
The Store Coupons widget displays coupons from a specific store with a modern layout:
- Desktop view shows store logo on the left and coupon details on the right
- Mobile view displays coupons as cards with the store logo at the top
- Clicking on a coupon button opens a popup with the coupon code
- Clicking on the code copies it to clipboard and opens the store website
- Fully customizable through Elementor controls

## Requirements
- WordPress 5.0 or higher
- Elementor 2.0 or higher

## Changelog

### 1.13.0
- Added ascending order control to Coupon Widget
- Implemented toggle switch to display coupons in ascending order (oldest first) or descending order (newest first)
- Applied ordering control to all data source types (Latest, Expiring Soon, Exclusive, Verified)
- Enhanced user control over coupon display sequence in Elementor editor

### 1.12.0
- Added new Store Carousel widget for displaying multiple selected stores in a carousel format
- Implemented multiple store selection capability with preserved selection order
- Added comprehensive carousel settings (slides to show, autoplay, infinite loop, etc.)
- Added navigation controls with customizable arrows and dots
- Added responsive breakpoints for optimal display on all device sizes
- Added full styling controls for all carousel elements
- Integrated Slick Carousel library for smooth sliding functionality

### 1.11.7
 - Resolved 'store-coupons-widget.js' not find error.

### 1.8.5
- Added pagination icon size control in Store Categories widget
- Improved pagination icon styling and alignment
- Added support for both Font Awesome icons and SVG icons in pagination

### 1.8.4
- Fixed pagination styling in Store Categories widget to match Store widget
- Updated CSS selectors for better pagination appearance
- Improved pagination controls for better user experience

### 1.8.3
- Added "Show All Categories" option to Store Categories widget
- Added pagination support for Store Categories widget
- Added pagination styling controls in Elementor
- Added option to choose between numbers only or numbers with prev/next pagination
- Added custom icons for pagination navigation

### 1.8.2
- Changed grid layout to use fixed-width columns of 150px
- Replaced column count control with column width control
- Implemented auto-fill grid for responsive layout
- Improved grid layout to automatically adjust based on available space

### 1.8.1
- Limited Store Categories widget box width to 150px by default
- Added width control to the widget for better customization
- Improved responsive layout for the Store Categories widget

### 1.8.0
- Added Store Categories widget for Elementor
- Added grid layout for displaying store categories
- Added option to choose between image or icon for categories
- Added category meta fields for uploading images and selecting icons
- Added full styling controls in Elementor for the widget
- Added scripts and styles handler for better asset management

### 1.7.0
- Added Store CPT Slug Changer feature
- Added ability to change store post type URL from /store/ to any custom slug
- Integrated CPT slug changer with Taxonomy URL Rewriter settings
- Added automatic permalink filtering for store URLs
- Improved admin interface with separate sections for different URL settings

### 1.6.2
- Removed automatic store categories list from the main page
- Prepared for future custom widget implementation
- Kept clean URL structure and redirects

### 1.6.1
- Added automatic redirection from old URLs to new URLs
- Fixed issue where both old and new URLs were accessible
- Implemented 301 permanent redirects for better SEO
- Ensured only the new URL structure works for accessing categories

### 1.6.0
- Completely redesigned taxonomy URL rewriting system
- Implemented proper WordPress rewrite rules to change taxonomy URLs
- Added ability to use page slug instead of taxonomy slug in URLs
- Fixed issues with individual category URLs
- Added automatic term list to the main page
- Added term information to category pages
- Improved compatibility with all themes and plugins

### 1.5.1
- Fixed issue with individual category URLs (now using original URLs)
- Fixed issue with page content showing on all pages
- Improved content display to only show on linked pages
- Maintained clean URLs for store categories

### 1.5.0
- Completely redesigned taxonomy-page linking system for full Elementor compatibility
- Added term meta approach instead of URL rewriting for better reliability
- Added individual page linking for each store category
- Added default page option for all categories
- Improved JavaScript-based term information display
- Added admin interface in both Stores menu and Settings menu

### 1.4.28
- Completely rewrote Elementor editor detection for maximum compatibility
- Implemented JavaScript-based approach for Elementor content rendering
- Fixed template handling for Elementor pages
- Improved page template detection and application

### 1.4.27
- Fixed Elementor editor compatibility issues with linked pages
- Fixed issue where Elementor design wasn't showing on linked pages
- Added proper Elementor content rendering for linked pages
- Improved detection of Elementor editor to prevent conflicts

### 1.4.26
- Fixed admin menu visibility issue for Taxonomy Linker
- Added Taxonomy Linker to Settings menu for easier access
- Improved initialization timing for better compatibility

### 1.4.25
- Added Taxonomy Linker feature to link store_category taxonomy with any WordPress page
- Custom URL structure: page-slug/term-slug instead of store-category/term-slug
- Automatic redirection from old URLs to new URLs
- Full Elementor compatibility for designing linked pages
- Added admin interface to configure taxonomy linking

### 1.4.24
- Added singleton pattern for better Elementor integration
- Improved initialization timing with elementor/init hook
- Enhanced compatibility with all Elementor Pro versions
- Fixed potential initialization issues

### 1.4.23
- Fixed PHP error with undefined method get_templates_for_location()
- Added compatibility with different Elementor Pro versions
- Improved template path detection with multiple fallback options
- Enhanced template override mechanism for better reliability

### 1.4.22
- Fixed critical issue with Elementor templates not applying to single store pages
- Implemented proven solution from Elementor community for template overrides
- Added direct template path override for better compatibility
- Added high-priority filters to ensure template selection works correctly
- Improved template condition detection for store post type

### 1.4.21
- Fixed issue with Elementor templates not applying to single store pages
- Simplified Elementor integration to use native Elementor locations
- Removed unnecessary custom template locations
- Improved compatibility with different Elementor Pro versions

### 1.4.20
- Completely redesigned Elementor integration for better compatibility
- Added store_category taxonomy to Elementor's public taxonomies
- Added store post type to Elementor's public post types
- Simplified archive handling for better performance
- Removed custom conditions in favor of Elementor's built-in conditions

### 1.4.19
- Fixed PHP error in Elementor condition class
- Improved compatibility with Elementor Pro
- Fixed method signature to match parent class

### 1.4.18
- Added custom Elementor condition for Store Categories List page
- Added separate Elementor Theme Builder location for main store category archive
- Enhanced Elementor Pro integration for custom archive templates
- Improved template handling for better compatibility with Elementor

### 1.4.17
- Added Elementor Theme Builder integration for store category archives
- Improved archive handling with custom query modifications
- Enhanced compatibility with Elementor templates
- Fixed 404 errors on store category pages

### 1.4.16
- Fixed store category archive pages with improved rewrite rules
- Removed unnecessary template files
- Enhanced taxonomy registration for better permalink handling

### 1.4.15
- Added custom templates for store category archive pages
- Added main store category archive page to display all categories
- Added custom rewrite rules for better URL handling
- Added template loader for custom taxonomy templates
- Improved styling for store category pages

### 1.4.14
- Fixed issue with store category archive and single pages returning 404 errors
- Improved taxonomy registration with proper rewrite rules
- Added smart rewrite rules flushing mechanism to avoid performance issues
- Added admin notice and button to manually flush rewrite rules when needed
- Enhanced permalink structure for store categories

### 1.4.13
- Fixed Arrow Color and Arrow Size controls not working in Related Stores widget
- Added new Arrow Background Color control for Related Stores widget
- Improved CSS to allow Elementor controls to override default styles
- Enhanced hover effect to respect custom background colors
- Removed fixed colors and sizes from CSS to allow for better customization

### 1.4.12
- Fixed navigation arrows not appearing in Related Stores widget
- Improved carousel navigation with better visibility and positioning
- Enhanced Font Awesome integration for better icon support
- Added debugging information for troubleshooting carousel issues
- Improved CSS specificity for carousel navigation elements

### 1.4.6
- Completely redesigned icon spacing implementation using CSS variables
- Fixed persistent issue with icon spacing control not working in Elementor
- Removed inline styles in favor of CSS variable approach
- Added default spacing value with proper fallback mechanism

### 1.4.5
- Fixed persistent issue with icon spacing control in Store FAQ Widget
- Implemented direct inline styles for icon spacing
- Added dynamic PHP-generated spacing based on Elementor settings
- Ensured spacing works correctly for both left and right icon positions

### 1.4.4
- Fixed issue with icon spacing control not working in the Store FAQ Widget
- Updated CSS selectors to properly target icon elements
- Improved consistency between Elementor controls and CSS styles
- Enhanced spacing control for better user experience

### 1.4.3
- Fixed issue with "Allow Multiple Open Items" option not working correctly
- Improved JavaScript to properly read data attributes
- Added debugging information to help troubleshoot accordion behavior
- Enhanced attribute handling for better reliability

### 1.4.2
- Fixed persistent issue with "Open First Item" option not working correctly
- Improved JavaScript initialization to properly handle the first FAQ item
- Added additional data attributes for better debugging
- Enhanced content visibility handling for the first item

### 1.4.1
- Fixed issue with Store FAQ Widget icon not changing when opening/closing items
- Fixed issue with "Open First Item" and "Allow Multiple Open Items" options not working correctly
- Improved JavaScript for better handling of accordion functionality
- Enhanced icon toggling with separate elements for expanded and collapsed states

### 1.4.0
- Added new Store FAQ Widget for displaying store FAQs in an accordion style
- Added full styling controls for FAQ questions, answers, and icons
- Added options for icon placement (left/right)
- Added options for opening/closing behavior of accordion items
- Added widget title customization options
- Added support for rich text in FAQ answers
- Optimized for mobile responsiveness

### 1.3.3
- Fixed persistent issue with store categories not displaying in admin column
- Completely rewrote the category column implementation using direct database queries
- Enhanced taxonomy registration with full labels and capabilities
- Improved column ordering and display
- Added more robust error handling for taxonomy operations
- Optimized database queries for better performance

### 1.3.2
- Fixed issue with store categories not displaying in the admin list
- Improved taxonomy registration order to ensure proper functionality
- Added explicit taxonomy relationship registration
- Enhanced debugging for category display issues
- Fixed taxonomy and post type integration

### 1.3.1
- Added category column to Store CPT admin list
- Added category filter dropdown to Store CPT admin list
- Made category column sortable
- Improved store management with better category visibility
- Added ability to filter stores by category similar to network filter

### 1.3.0
- Removed Coupon Categories and Coupon Tags taxonomies from Coupon CPT
- Added Store Categories taxonomy to Store CPT
- Coupons now automatically inherit categories from their associated store
- Updated admin columns to show store categories in coupon list
- Added filtering of coupons by store categories
- Improved user experience by simplifying category management
- Categories are now managed at the store level only

### 1.2.0
- Added search functionality to the stores dropdown in coupon edit screen
- Implemented Select2 library for enhanced dropdown experience
- Added placeholder text for better user guidance
- Improved user experience when selecting from many stores
- Added clear button to easily reset store selection

### 1.1.9
- Added option to show/hide expired coupons
- Expired coupons now hidden by default on the frontend
- Added special handling to show expired coupons in Elementor editor mode
- Improved query to filter out expired coupons based on expiry date
- Added detailed description for the expired coupons control

### 1.1.8
- Fixed expiry badge not showing for coupons with expiry dates
- Added support for showing expired coupons with "Expired" label
- Added different styling for expired vs active coupon badges
- Improved date formatting for expiry badges
- Added "Expires:" prefix to expiry date for better clarity

### 1.1.7
- Fixed expiry badge not showing when enabled
- Updated Font Awesome icon classes for badges from fa to fas
- Changed calendar icon to calendar-alt for better visibility
- Updated lock icon in coupon meta section
- Ensured proper display of verified and expiry badges

### 1.1.6
- Fixed icon display issue in Get Deal and Print Code buttons
- Updated Font Awesome from version 4.7.0 to 5.15.4
- Added both Font Awesome 5 and 4 for better compatibility
- Updated icon classes from 'fa' to 'fas' for Font Awesome 5 compatibility
- Ensured proper icon display across all button types

### 1.1.5
- Fixed box-shadow not showing with clip-path by implementing wrapper approach
- Added store-coupon-item-wrapper element to contain each coupon item
- Applied filter: drop-shadow() to wrapper element for proper shadow with clip-path
- Improved hover effects with enhanced shadow transitions
- Implemented solution based on CSS-Tricks article about box-shadows and clip-path

### 1.1.4
- Fixed box-shadow not showing on coupon items with zigzag pattern
- Added pseudo-element to display shadow behind zigzag pattern
- Enhanced shadow effect for better visual appearance
- Improved hover effects for coupon items
- Increased shadow depth for more premium look

### 1.1.3
- Fixed critical issue with Get Code button not displaying
- Changed button positioning from absolute to relative
- Added minimum width to ensure button visibility
- Improved code preview positioning and text shadow
- Fixed container overflow issues
- Enhanced overall button appearance and usability

### 1.1.2
- Completely redesigned Get Code button with folded corner effect
- Implemented clip-path polygon for the button shape
- Added realistic folded paper effect with transparent border
- Added subtle shadow effect for the folded corner
- Improved code preview positioning and styling
- Enhanced overall premium look and feel of the Get Code button

### 1.1.1
- Implemented folded corner effect for Get Code button
- Added realistic folded paper effect with shadow
- Improved button styling for a more premium look
- Separated button into two distinct parts with proper styling
- Enhanced visual appeal of the code preview section

### 1.1.0
- Fixed button overlap with zigzag pattern
- Added proper spacing between buttons and zigzag pattern
- Added right padding to coupon items with zigzag pattern
- Improved z-index for buttons to ensure they appear above zigzag pattern
- Enhanced overall premium feel of coupon buttons

### 1.0.99
- Fixed critical issue with zigzag patterns not showing on mobile or with all-sides option
- Completely revised CSS selector specificity for zigzag patterns
- Added dual selectors for both right-side and all-sides patterns
- Removed problematic !important rule that was blocking pattern display
- Improved CSS structure for better cross-browser compatibility

### 1.0.98
- Further improved mobile zigzag pattern implementation
- Fixed CSS specificity issues for mobile zigzag patterns
- Removed empty CSS rulesets
- Optimized mobile CSS for better performance

### 1.0.97
- Fixed mobile zigzag pattern not appearing on all sides
- Added !important rule to ensure mobile zigzag patterns override desktop styles
- Improved CSS specificity for mobile zigzag patterns
- Ensured consistent zigzag appearance across all device sizes

### 1.0.96
- Added higher density zigzag pattern options (up to 40 points)
- Added new density options: 12, 15, 20, 25, 30, and 40 points
- Implemented optimized CSS for higher density patterns using CSS variables
- Added simplified mobile patterns for higher densities to ensure good performance
- Improved zigzag pattern rendering for all device sizes

### 1.0.95
- Fixed zigzag pattern density control not working
- Changed density control from slider to dropdown for better precision
- Implemented class-based approach for zigzag patterns instead of attribute selectors
- Added specific patterns for densities 2, 3, 4, 5, 6, 8, and 10
- Improved mobile responsiveness with proper class-based selectors
- Fixed all-sides zigzag pattern on mobile devices

### 1.0.94
- Fixed zigzag pattern density control to properly increase number of zigzags
- Implemented individual zigzag patterns for each density level (2-10)
- Created specific patterns for right-side only zigzag with different densities
- Created specific patterns for all-sides zigzag with different densities
- Improved mobile responsiveness with proper zigzag patterns at all densities
- Fixed issue where increasing density would make zigzag pattern appear as a straight line

### 1.0.93
- Added Elementor controls for zigzag pattern customization
- Added option to enable/disable zigzag pattern
- Added zigzag size control with slider
- Added zigzag density control with slider
- Added option to choose between right-side only or all-sides zigzag pattern
- Implemented responsive design with all-sides zigzag on mobile devices
- Added CSS variables for dynamic zigzag pattern styling

### 1.0.92
- Implemented exact zigzag pattern on right side of coupons using CSS clip-path
- Matched the zigzag pattern to the reference image
- Removed previous zigzag implementation
- Improved coupon appearance with clean cut edges

### 1.0.91
- Fixed zigzag pattern to appear as a cutting edge on the right side of coupons
- Redesigned Get Code button to match the reference design with proper folded corner effect
- Improved button styling with proper revealing look
- Fixed positioning of code preview element

### 1.0.90
- Reduced store logo size to 200x200 pixels
- Added zigzag pattern to the right side of coupons using CSS
- Redesigned Get Code button with a revealing fold effect
- Improved button styling with a folded corner appearance
- Updated code preview element to match the design reference

### 1.0.89
- Fixed namespace issue with WP_Query in Store Coupons widget
- Added proper global namespace reference to WP_Query

### 1.0.88
- Added pagination to Store Coupons widget
- Added filter functionality for coupons (verified, exclusive, code, deal)
- Removed number of coupons option in favor of pagination
- Added show/hide controls for each filter type
- Added CSS styling for filters and pagination
- Improved user experience with active filter highlighting
- Added page navigation with previous/next buttons

### 1.0.87
- Added styling controls for coupon title with color and typography options
- Added styling controls for badges (exclusive, verified, expiry)
- Added show/hide options for all functionality elements:
  - Show/hide store logo
  - Show/hide coupon title
  - Show/hide exclusive badge
  - Show/hide verified badge
  - Show/hide expiry badge
  - Show/hide description
  - Show/hide button
- Improved badge styling with individual color controls
- Added common badge styling options (border radius, padding, margin)

### 1.0.86
- Added icon controls for individual buttons (Get Deal, Print Code)
- Added complete popup styling controls in Elementor widget
- Added controls for popup background, text, and heading colors
- Added controls for popup code display styling
- Added controls for popup copy button styling
- Implemented icon selection for Get Deal and Print Code buttons
- Added support for both Font Awesome and SVG icons

### 1.0.85
- Removed unnecessary Coupon Item style section
- Fixed button color controls to properly apply background and text colors
- Improved hover state styling for all buttons
- Ensured direct CSS property application for better compatibility

### 1.0.84
- Updated version number to ensure all styling changes are applied correctly

### 1.0.83
- Added individual border controls for each button type (Get Deal, Print Code)
- Added border radius control for code preview element
- Improved border controls with Elementor's standard dimensions control
- Added ability to set different border styles (solid, dashed, dotted) for each button
- Enhanced styling flexibility with individual border width controls

### 1.0.82
- Added CSS variables for complete control over button styling
- Implemented separate styling controls for each button type (Get Code, Get Deal, Print Code)
- Added customizable properties for code preview element
- Made all visual aspects of buttons easily customizable through CSS variables
- Maintained full functionality while providing enhanced styling flexibility

### 1.0.81
- Fixed code preview display by adding width: 80% to ensure code is visible
- Improved ghost button element sizing for better visual appearance

### 1.0.80
- Completely redesigned "Get Code" button with layered effect
- Implemented exact design as provided in the example code
- Created a unique layered button with main button and ghost outline
- Maintained all existing functionality while improving visual design
- Ensured proper z-index and positioning for layered elements

### 1.0.79
- Completely redesigned button styles to match the Couponly theme
- Updated all buttons with proper rounded corners (30px border-radius)
- Improved "Get Code" button with better layering and styling
- Enhanced "Get Deal" and "Print Code" buttons with consistent styling
- Added proper transitions and hover effects for all buttons

### 1.0.78
- Improved button designs to exactly match the provided mockups
- Updated "Get Code" button to show only the last 2 characters of the code
- Redesigned "Get Code" button to appear as two distinct layers
- Updated "Get Deal" button with proper styling and rounded corners
- Enhanced visual appearance of all buttons with proper shadows and hover effects

### 1.0.77
- Added different button styles for each coupon type:
  - Online Sale: "Get Deal" button with light blue outline and arrow
  - In-store Code: "Print Code" button with light blue outline and arrow
  - Online Code: "GET CODE" button with solid blue background and code preview
- Improved visual distinction between different coupon types
- Enhanced user experience with more intuitive button designs

### 1.0.76
- Removed unused JavaScript files to improve performance and reduce plugin size
- Cleaned up assets directory by removing deprecated and unused JS files
- Optimized codebase by removing redundant files

### 1.0.75
- Added verified badge for coupons that are marked as verified
- Implemented verified badge with check-circle icon and light blue styling
- Improved visual distinction between exclusive and verified coupons

### 1.0.74
- Completely redesigned solution for affiliate links opening twice issue
- Implemented proper event unbinding to prevent duplicate event handlers
- Added initialization flag to prevent multiple widget initializations
- Enhanced openLink function with better validation and error recovery
- Improved Elementor hook management to prevent duplicate handlers
- Fixed race condition between popup display and link opening
- Added extensive logging for better debugging

### 1.0.73
- Fixed persistent issue with affiliate links opening twice
- Implemented robust timer management with clearTimeout
- Added proper timer variable to track and clear setTimeout instances
- Enhanced openLink function with better error handling and validation
- Added checks to prevent any possibility of duplicate link opening
- Fixed race condition between click tracking and link opening

### 1.0.72
- Fixed critical issue with couponId not being passed to showPopup function
- Added couponId parameter to showPopup function call and definition
- Added error handling for couponId in usage count AJAX call
- Added additional logging for debugging
- Fixed JavaScript error: "Uncaught ReferenceError: couponId is not defined"
- Improved JSDoc documentation for function parameters

### 1.0.71
- Version bump for compatibility testing

### 1.0.70
- Version bump for WordPress plugin updates

### 1.0.69
- Fixed minor styling issues in popup display
- Improved mobile responsiveness for popup

### 1.0.68
- Fixed critical logical error in click tracking
- Completely restructured JavaScript with proper separation of concerns
- Implemented single-responsibility functions for better maintainability
- Centralized click tracking to prevent duplicate tracking
- Added dedicated functions for popup display and link opening
- Improved error handling and logging throughout the code

### 1.0.67
- Fixed nonce implementation in AJAX requests
- Added detailed logging for nonce usage
- Added more debugging information for hasOpenedLink flag
- Improved documentation for nonce implementation
- Fixed potential race condition in AJAX requests

### 1.0.64
- Added extensive debugging with console.log statements
- Added detailed logging for initialization process
- Added logging for button click events
- Added logging for AJAX requests and link opening
- Added logging for hasOpenedLink flag status
- Improved error detection capabilities

### 1.0.63
- Fixed persistent issue with affiliate link opening twice
- Completely rewrote JavaScript with a much simpler approach
- Used global flag variable to prevent duplicate link opening
- Simplified AJAX tracking with complete callback
- Removed all unused JavaScript files
- Ensured proper sequence of operations

### 1.0.62
- Fixed critical issue with affiliate link opening twice
- Added flag to prevent duplicate link opening
- Implemented callback-based approach for tracking and link opening
- Fixed nonce verification issue in AJAX handler
- Completely rewrote JavaScript with more robust architecture
- Added error handling for AJAX requests

### 1.0.61
- Fixed issue with affiliate link opening twice
- Moved tracking code to execute only once before opening the link
- Combined tracking and link opening into a single operation
- Improved overall user experience with more reliable behavior
- Ensured consistent tracking of coupon usage statistics

### 1.0.60
- Fixed popup not showing issue by reverting to a more stable approach
- Reordered code execution sequence for better reliability
- Moved code copying after popup is fully visible
- Increased delay for affiliate link opening to 1.5 seconds
- Simplified JavaScript structure while maintaining functionality

### 1.0.59
- Fixed critical issue with popup not showing and code not copying
- Completely restructured JavaScript code for better reliability
- Increased delay between popup display and link opening
- Added dedicated function for showing popup
- Improved sequence of operations for better user experience

### 1.0.58
- Added automatic code copying when popup opens
- Implemented modern Clipboard API with fallback for older browsers
- Added visual feedback when code is automatically copied
- Improved user experience by eliminating need to click on code
- Enhanced error handling for clipboard operations
- Maintained manual click functionality as backup

### 1.0.57
- Fixed code copying functionality in popup
- Fixed popup close button not working
- Improved event handling for dynamically created popup elements
- Removed duplicate code in JavaScript
- Enhanced user experience with better popup interaction

### 1.0.55
- Fixed popup width issue by changing display method
- Increased popup width to 750px for better user experience
- Improved popup positioning with vertical centering
- Added responsive scrolling for popups on smaller screens
- Fixed popup content alignment for better readability

### 1.0.54
- Increased popup width for better user experience
- Improved popup styling with larger padding and font sizes
- Added real usage count tracking system
- Implemented automatic usage count increment on button click
- Added AJAX handler for retrieving coupon usage statistics
- Enhanced code container with better styling and border radius

### 1.0.53
- Added new Store Coupons widget for single store pages
- Implemented desktop layout with store logo on left and coupon details on right
- Added mobile responsive card layout
- Created popup functionality for coupon codes with auto-copy feature
- Added affiliate link opening in new window
- Implemented coupon click tracking system
- Added Font Awesome icons for better visual indicators
- Styled widget to match the provided design mockup

## Previous Versions

### 1.0.52
- Fixed syntax error in Store Coupons Widget
- Improved code structure to avoid PHP parsing issues
- Refactored render method to use string concatenation instead of mixed PHP/HTML syntax
- Enhanced code reliability and maintainability

### 1.0.51
- Version bump for compatibility with WordPress plugin updates

### 1.0.50
- Added new Store Coupons Widget for displaying coupons from a specific store
- Implemented popup functionality for coupon code display
- Added coupon usage tracking functionality
- Created dedicated CSS and JavaScript files for the Store Coupons Widget
- Improved mobile responsiveness with card layout for small screens
- Added copy-to-clipboard functionality for coupon codes
- Enhanced user experience with visual feedback when copying codes

### 1.0.49
- Fixed social media icons not displaying in the share section
- Added Font Awesome library for social media icons
- Improved icon styling with larger size and better alignment
- Enhanced icon visibility with proper spacing and dimensions
- Added hover effects for better user interaction
- Ensured consistent display across different browsers

### 1.0.48
- Added layout controls to customize button and social share positions
- Added option to switch button between left and right sides
- Added option to switch social share between left and right sides
- Added responsive control to display elements in column (vertical) layout
- Added full-width layout option where each element takes 50% width
- Improved mobile responsiveness with automatic stacking

### 1.0.47
- Completely restructured layout to match the provided mockup exactly
- Placed "Read More" link at the end of truncated text (same line)
- Positioned "Share on:" section horizontally aligned with the button
- Created a flex layout for button and social share to be on the same row
- Improved responsive design for all screen sizes
- Fixed paragraph formatting for proper text display

### 1.0.46
- Improved "Read More" link to appear inline with text instead of on a new line
- Enhanced social sharing to display icons directly next to the "Share on:" text
- Improved UI layout to match the provided design mockup
- Added "Show Less" functionality for expanded content
- Fixed CSS for better alignment of social icons
- Optimized JavaScript for better user experience

### 1.0.45
- Enhanced Store Description widget with button alignment controls
- Added description word limit with "Read More" functionality
- Implemented social media sharing options (Facebook, Instagram, WhatsApp, TikTok)
- Added customizable social share icons with hover effects
- Added alignment controls for social share section
- Improved responsive design for all new features

### 1.0.44
- Added new Store Description widget for single store pages
- Created dedicated CSS file for Store Description widget
- Implemented responsive layout with left thumbnail and right content
- Added customizable button with hover effects
- Added Elementor controls for all widget elements
- Designed for use in Elementor Theme Builder templates

### 1.0.43
- Created dedicated external CSS file for coupon widget
- Added custom image size (600x450) for printable coupons
- Removed inline styles from coupon widget for better maintainability
- Improved coupon widget styling with consistent aspect ratios
- Applied DRY principles to coupon widget styling

### 1.0.42
- Implemented WordPress custom image size (250x200) for store thumbnails
- Added proper image regeneration support through WordPress media system
- Optimized CSS to work with the new image size
- Simplified thumbnail styling for better performance
- Ensured consistent image display across all stores

### 1.0.41
- Fixed store image display to ensure consistent sizing across all images
- Changed from aspect-ratio to fixed height for better image consistency
- Updated image styling to use object-fit: contain for proper image display
- Prevented image cropping while maintaining uniform appearance
- Ensured logos and promotional images display properly regardless of dimensions

### 1.0.40
- Implemented proper CSS architecture following WordPress best practices
- Added external CSS file for store widget styling
- Maintained backward compatibility with inline styles
- Added documentation comments for future maintenance
- Prepared for DRY (Don't Repeat Yourself) approach for future widgets

### 1.0.39
- Updated inline CSS for store thumbnails to use aspect-ratio and object-fit
- Modified store thumbnail styling to ensure square images with proper proportions
- Added hover zoom effect for store images
- Improved image display to prevent stretching or cutting
- Maintained compatibility with existing widget structure

### 1.0.38
- Improved store image display with consistent sizing
- Added CSS with aspect-ratio and object-fit properties for uniform store images
- Implemented responsive grid layout for store widget
- Added hover effects for better user experience
- Ensured images maintain proper proportions without stretching or cutting

### 1.0.37
- Enhanced cascading delete functionality for trashed stores
- When a store is permanently deleted from trash, all its related coupons in trash are also permanently deleted
- Improved data cleanup to ensure no orphaned coupons remain in any state
- Added specific handling for trashed coupons during store deletion

### 1.0.36
- Added cascading delete functionality for stores and coupons
- When a store is deleted, all its related coupons are automatically deleted
- When a store is trashed, all its related coupons are automatically trashed
- When a store is restored from trash, all its related coupons are automatically restored
- Works with both single store deletion and bulk store deletion

### 1.0.35
- Fixed issue with widget title not showing after pagination
- Added widget title rendering to AJAX handler
- Ensured consistent appearance between initial load and paginated views
- Maintained widget title styling across all pagination pages

### 1.0.34
- Fixed critical issue with pagination styling not being applied on subsequent pages
- Fixed class name inconsistency between main widget and AJAX handler
- Changed store grid class from 'store-grid' to 'store-list' in AJAX handler
- Ensured consistent styling across all pagination pages
- Improved user experience when navigating through store pages

### 1.0.33
- Added widget title styling controls in the General Style section
- Added widget title color, typography, alignment, and margin controls
- Updated the widget icon to a more recognizable shopping cart icon
- Improved organization of style controls with separators
- Maintained both store title and widget title styling capabilities

### 1.0.32
- Added comprehensive styling controls for store titles
- Added text color and hover color controls
- Added typography controls for font family, size, weight, etc.
- Added text alignment options (left, center, right)
- Added margin and padding controls for fine-tuning layout
- Reorganized style sections for better organization

### 1.0.31
- Changed pagination to be disabled by default for clearer behavior
- Improved descriptions for "Number of Stores" and "Stores Per Page" settings
- Clarified when each setting is used to avoid confusion
- Made the default behavior more intuitive for new users
- Fixed conflict between pagination and "Number of Stores" setting

### 1.0.30
- Fixed issue with "Number of Stores" control not showing after disabling "Show All Stores"
- Made "Number of Stores" control always visible for better user experience
- Added detailed comments to clarify where and how the Number of Stores setting is used
- Improved debug capabilities for troubleshooting
- Synchronized AJAX handler with main widget for consistent behavior

### 1.0.29
- Fixed critical bug where "Number of Stores" setting wasn't limiting the number of stores displayed
- Completely rewrote the query logic to ensure proper store limiting
- Synchronized AJAX handler with main widget to ensure consistent behavior
- Simplified conditional logic for better reliability
- Added explicit type casting for numeric settings to prevent type errors

### 1.0.28
- Fixed issue with "Stores per page" option not showing when toggling "Show All Stores"
- Fixed issue with "Number of Stores" option not showing after disabling "Show All Stores"
- Fixed bug where "Number of Stores" setting wasn't limiting the number of stores displayed
- Improved conditional logic for widget controls

### 1.0.27
- Implemented AJAX-based pagination for better performance
- Added caching system for store counts to improve query efficiency
- Added loading indicator for AJAX pagination
- Optimized database queries for large store collections
- Made store widget fully compatible with sites having 1000+ stores

### 1.0.26
- Added "Show All Stores (Archive Mode)" option for store archive pages
- Implemented pagination that works with unlimited store display
- Improved store query to support archive mode while maintaining pagination
- Made pagination controls compatible with archive mode
- Fixed conditional display of pagination controls in Elementor editor

### 1.0.25
- Added hover effects for the View Coupons button
- Added tabbed controls for Normal and Hover states
- Added ability to customize hover text color, background color, border radius, and padding
- Added transition duration control for smooth hover effects
- Improved button styling with better text alignment and cursor pointer

### 1.0.24
- Added customizable text format for coupon count badge
- Added option to align badge inline with title or right-aligned
- Improved badge styling to accommodate text
- Made badge width auto-adjust to content
- Enhanced title wrapper layout for better alignment options

### 1.0.23
- Added coupon count badge next to store title
- Added option to show/hide coupon count badge
- Added styling controls for coupon count badge
- Wrapped store title and coupon count in a div for better layout
- Added spacing controls between image and title, and between title and button
- Improved store item layout with flexible spacing options

### 1.0.22
- Improved pagination styling by removing background color from ul.page-numbers
- Changed pagination styling controls to use tabs for Normal, Hover, and Active states
- Added !important rules to ensure ul and li elements remain transparent
- Enhanced user experience in Elementor editor with tabbed controls

### 1.0.21
- Enhanced pagination with customizable icons
- Added icon picker for previous and next pagination buttons
- Added option to show/hide previous and next buttons
- Improved pagination styling for better appearance
- Fixed icon alignment in pagination

### 1.0.20
- Fixed namespace issue with WP_Query in store widget
- Added proper global namespace reference to WP_Query

### 1.0.19
- Added pagination to store widget
- Added customizable pagination styles through Elementor
- Added option to enable/disable pagination
- Added control for number of stores per page
- Added pagination alignment options
- Improved store widget query to support pagination

### 1.0.18
- Simplified store widget design based on client requirements
- Removed discount badge from store widget
- Removed network display from store widget
- Removed "Shop Now" button, keeping only "View Coupons" button
- Made store images consistent in size and layout
- Added image height control for store thumbnails

### 1.0.17
- Added button to store widget that redirects to the store URL
- Added discount badge option to store widget
- Added customization options for button and discount badge styling
- Improved store widget layout and design

### 1.0.16
- Fixed Elementor widgets not appearing in Elementor editor
- Updated Elementor widget registration to support Elementor 3.5.0+
- Improved plugin structure for better Elementor integration
- Fixed duplicate loading of Elementor widgets

### 1.0.15
- Applied best practices from WordPress community for filter persistence
- Added is_main_query() check to prevent filter interference
- Used query->set() method instead of directly modifying query_vars
- Improved meta_query handling for better compatibility

### 1.0.14
- Implemented a new approach for store filters using only stores that are used in coupons
- Used direct database query to get unique store IDs from coupon meta
- Simplified filter implementation to use WordPress's native filter system
- Removed JavaScript injection for better compatibility with WordPress admin

### 1.0.13
- Completely redesigned store filter to ensure all stores are always shown
- Added debug logging to track store filter values
- Improved store query to include all post statuses
- Fixed issue where store dropdown was losing options after filtering

### 1.0.12
- Enhanced store filter to properly preserve selected values
- Added hidden input to maintain store filter value when other filters are applied
- Improved JavaScript to ensure filter values persist across form submissions
- Fixed issue where store filter value was being lost when filtering

### 1.0.11
- Completely redesigned store filter implementation using JavaScript injection
- Separated store filter from other filters to prevent DOM manipulation issues
- Added dynamic store filter that's injected into the admin interface
- Fixed filter persistence issue by preserving filter values across form submissions

### 1.0.10
- Simplified filter implementation to work with WordPress's native filter system
- Removed custom filter button to prevent duplicate buttons
- Added auto-submit functionality for immediate filtering
- Added proper screen reader labels for accessibility

### 1.0.9
- Completely redesigned filter approach to use WordPress's native filter system
- Used JavaScript to dynamically add hidden inputs to preserve filter values
- Simplified filter HTML structure to prevent DOM manipulation issues
- Fixed filter persistence issue by using a single form with dynamic inputs

### 1.0.8
- Implemented separate forms for each filter to ensure persistence
- Added auto-submit functionality with JavaScript
- Preserved filter values across different filter selections
- Fixed all filter persistence issues with a completely new approach

### 1.0.7
- Completely redesigned admin filters to ensure they always remain visible
- Added wrapper div for filters with custom CSS
- Improved filter UI and user experience
- Fixed all filter persistence issues

### 1.0.6
- Fixed filter persistence issue with hidden fields
- Added proper category filter dropdown
- Implemented category filtering functionality
- Ensured all filters work together correctly

### 1.0.5
- Fixed filter persistence issue in Coupon admin interface
- Updated Store dropdowns to show all store statuses (published, draft, etc.)
- Improved admin interface filtering logic

### 1.0.4
- Added Network and Image columns to Store admin interface
- Reordered Store columns to: Title, Networks, Date, Image
- Added Store, Category, and Status filters to Coupon admin interface
- Added Store column to Coupon admin interface
- Added Exclusive/Verified status indicators to Coupon admin interface

### 1.0.3
- Completely restructured Elementor integration
- Moved widget classes to separate files
- Fixed compatibility issues with Elementor

### 1.0.2
- Fixed Elementor namespace issues
- Updated Elementor widget class references

### 1.0.1
- Fixed Elementor integration
- Improved widget registration

### 1.0.0
- Initial release
