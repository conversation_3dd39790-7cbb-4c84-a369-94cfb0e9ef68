<?php
/**
 * Store Meta Box
 */
class Store_Meta_Box {
    /**
     * Register meta boxes
     */
    public function register() {
        add_meta_box(
            'store_details',
            __('Store Details', 'advance-coupon'),
            array($this, 'render_store_details'),
            'store',
            'normal',
            'high'
        );

        add_meta_box(
            'store_faqs',
            __('Store FAQs', 'advance-coupon'),
            array($this, 'render_store_faqs'),
            'store',
            'normal',
            'high'
        );
    }

    /**
     * Render store details meta box
     */
    public function render_store_details($post) {
        // Add nonce for security
        wp_nonce_field('store_details_nonce', 'store_details_nonce');

        // Get saved values
        $store_url = get_post_meta($post->ID, '_store_url', true);
        $store_network = get_post_meta($post->ID, '_store_network', true);

        // Output fields
        ?>
        <table class="form-table">
            <tr>
                <th scope="row">
                    <label for="store_url"><?php _e('Store URL', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="url" id="store_url" name="store_url" value="<?php echo esc_url($store_url); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the URL of the store website', 'advance-coupon'); ?></p>
                </td>
            </tr>
            <tr>
                <th scope="row">
                    <label for="store_network"><?php _e('Store Network', 'advance-coupon'); ?></label>
                </th>
                <td>
                    <input type="text" id="store_network" name="store_network" value="<?php echo esc_attr($store_network); ?>" class="regular-text">
                    <p class="description"><?php _e('Enter the network this store belongs to', 'advance-coupon'); ?></p>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Render store FAQs meta box
     */
    public function render_store_faqs($post) {
        // Add nonce for security
        wp_nonce_field('store_faqs_nonce', 'store_faqs_nonce');

        // Get saved values
        $faq_count = get_post_meta($post->ID, '_faq_count', true);
        $faqs = get_post_meta($post->ID, '_store_faqs', true);

        if (empty($faqs)) {
            $faqs = array();
        }

        // Output fields
        ?>
        <div class="store-faqs-container">
            <p>
                <label for="faq_count"><?php _e('How many FAQs fields do you want?', 'advance-coupon'); ?></label>
                <input type="number" id="faq_count" name="faq_count" value="<?php echo esc_attr($faq_count); ?>" min="0" class="small-text">
                <button type="button" class="button" id="update_faq_count"><?php _e('Update', 'advance-coupon'); ?></button>
            </p>

            <div id="faqs_container">
                <?php
                if (!empty($faq_count) && $faq_count > 0) {
                    for ($i = 0; $i < $faq_count; $i++) {
                        $question = isset($faqs[$i]['question']) ? $faqs[$i]['question'] : '';
                        $answer = isset($faqs[$i]['answer']) ? $faqs[$i]['answer'] : '';
                        ?>
                        <div class="faq-item">
                            <h4><?php printf(__('FAQ #%d', 'advance-coupon'), $i + 1); ?></h4>
                            <p>
                                <label for="faq_question_<?php echo $i; ?>"><?php _e('Question', 'advance-coupon'); ?></label>
                                <input type="text" id="faq_question_<?php echo $i; ?>" name="faq_question[]" value="<?php echo esc_attr($question); ?>" class="widefat">
                            </p>
                            <p>
                                <label for="faq_answer_<?php echo $i; ?>"><?php _e('Answer', 'advance-coupon'); ?></label>
                                <?php
                                $editor_id = 'faq_answer_' . $i;
                                $settings = array(
                                    'textarea_name' => 'faq_answer[]',
                                    'textarea_rows' => 5,
                                    'media_buttons' => true,
                                );
                                wp_editor($answer, $editor_id, $settings);
                                ?>
                            </p>
                        </div>
                        <?php
                    }
                }
                ?>
            </div>
        </div>

        <script>
            jQuery(document).ready(function($) {
                // Handle updating FAQ count
                $('#update_faq_count').on('click', function() {
                    var count = parseInt($('#faq_count').val());
                    if (isNaN(count) || count < 0) {
                        count = 0;
                    }

                    // Save the current state to avoid losing data
                    var currentData = [];
                    $('.faq-item').each(function(index) {
                        var question = $(this).find('input[name="faq_question[]"]').val();
                        var answer = $(this).find('textarea[name="faq_answer[]"]').val();
                        currentData.push({
                            question: question,
                            answer: answer
                        });
                    });

                    // Clear the container
                    $('#faqs_container').empty();

                    // Add the new number of FAQ fields
                    for (var i = 0; i < count; i++) {
                        var question = (i < currentData.length) ? currentData[i].question : '';
                        var answer = (i < currentData.length) ? currentData[i].answer : '';

                        var html = '<div class="faq-item">';
                        html += '<h4><?php echo esc_js(__('FAQ #', 'advance-coupon')); ?>' + (i + 1) + '</h4>';
                        html += '<p>';
                        html += '<label for="faq_question_' + i + '"><?php echo esc_js(__('Question', 'advance-coupon')); ?></label>';
                        html += '<input type="text" id="faq_question_' + i + '" name="faq_question[]" value="' + question + '" class="widefat">';
                        html += '</p>';
                        html += '<p>';
                        html += '<label for="faq_answer_' + i + '"><?php echo esc_js(__('Answer', 'advance-coupon')); ?></label>';
                        html += '<textarea id="faq_answer_' + i + '" name="faq_answer[]" class="widefat" rows="5">' + answer + '</textarea>';
                        html += '</p>';
                        html += '</div>';

                        $('#faqs_container').append(html);
                    }
                });
            });
        </script>
        <style>
            .faq-item {
                margin-bottom: 20px;
                padding: 15px;
                background: #f9f9f9;
                border: 1px solid #e5e5e5;
            }
            .faq-item h4 {
                margin-top: 0;
            }
        </style>
        <?php
    }

    /**
     * Save meta box data
     */
    public function save($post_id) {
        // Check if nonce is set
        if (!isset($_POST['store_details_nonce']) || !isset($_POST['store_faqs_nonce'])) {
            return;
        }

        // Verify nonces
        if (!wp_verify_nonce($_POST['store_details_nonce'], 'store_details_nonce') || 
            !wp_verify_nonce($_POST['store_faqs_nonce'], 'store_faqs_nonce')) {
            return;
        }

        // Check if autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Save store details
        if (isset($_POST['store_url'])) {
            update_post_meta($post_id, '_store_url', esc_url_raw($_POST['store_url']));
        }

        if (isset($_POST['store_network'])) {
            update_post_meta($post_id, '_store_network', sanitize_text_field($_POST['store_network']));
        }

        // Save FAQ count
        if (isset($_POST['faq_count'])) {
            update_post_meta($post_id, '_faq_count', intval($_POST['faq_count']));
        }

        // Save FAQs
        if (isset($_POST['faq_question']) && isset($_POST['faq_answer'])) {
            $questions = $_POST['faq_question'];
            $answers = $_POST['faq_answer'];
            $faqs = array();

            for ($i = 0; $i < count($questions); $i++) {
                if (isset($questions[$i]) && isset($answers[$i])) {
                    $faqs[] = array(
                        'question' => sanitize_text_field($questions[$i]),
                        'answer' => wp_kses_post($answers[$i]),
                    );
                }
            }

            update_post_meta($post_id, '_store_faqs', $faqs);
        }
    }
}
