/* Store Categories Widget Styles */
.store-categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    grid-gap: 20px;
    width: 100%;
}

.store-category-item {
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 20px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    text-align: center;
    text-decoration: none;
    transition: all 0.3s ease;
    height: 200px;
    max-width: 150px;
    margin: 0 auto;
    overflow: hidden;
    width: 100%;
}

.store-category-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.store-category-image {
    margin-bottom: 15px;
    width: 100%;
    text-align: center;
}

.store-category-image img {
    width: 50%;
    height: auto;
    object-fit: cover;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.store-category-icon {
    margin-bottom: 15px;
    font-size: 50px;
    color: #333;
    transition: all 0.3s ease;
}

.store-category-title {
    margin: 0 0 10px;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    transition: all 0.3s ease;
}

.store-category-count {
    font-size: 14px;
    color: #777;
    transition: all 0.3s ease;
    background-color: #f5f5f5;
    padding: 3px 10px;
    border-radius: 20px;
}

/* Pagination Styles */
.store-categories-pagination {
    margin-top: 30px;
    text-align: center;
}

/* Fix for the pagination list style */
.store-categories-pagination .page-numbers {
    list-style: none;
    padding: 0;
    margin: 0;
    display: flex;
    justify-content: center;
    align-items: center;
}

.store-categories-pagination .page-numbers li {
    display: inline-block;
    margin: 0 3px;
}

.store-categories-pagination .page-numbers a,
.store-categories-pagination .page-numbers span {
    display: inline-block;
    padding: 8px 12px;
    min-width: 36px;
    text-align: center;
    background-color: #f7f7f7;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.3s ease;
}

.store-categories-pagination .page-numbers .current {
    background-color: #2271b1;
    color: #fff;
}

.store-categories-pagination .page-numbers a:hover {
    background-color: #e0e0e0;
}

.store-categories-pagination .page-numbers .next,
.store-categories-pagination .page-numbers .prev {
    padding: 8px 12px;
}

.store-categories-pagination .page-numbers .next i,
.store-categories-pagination .page-numbers .prev i {
    font-size: 12px;
    line-height: 1;
    vertical-align: middle;
}

.store-categories-pagination .page-numbers .next svg,
.store-categories-pagination .page-numbers .prev svg {
    width: 12px;
    height: 12px;
    vertical-align: middle;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .store-categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

@media (max-width: 767px) {
    .store-categories-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    .store-category-item {
        height: 180px;
    }

    .store-categories-pagination .page-numbers {
        padding: 6px 10px;
        margin: 0 2px;
    }
}
